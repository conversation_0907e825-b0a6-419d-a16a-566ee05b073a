"""
Comprehensive logging system for YouTube to Dailymotion pipeline
"""
import os
import sys
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path
from typing import Optional
import json
import traceback

from config import config

class PipelineLogger:
    """Enhanced logger with file rotation, structured logging, and monitoring"""
    
    def __init__(self, name: str = "youtube_pipeline"):
        self.name = name
        self.logger = logging.getLogger(name)
        self.setup_logging()
        
    def setup_logging(self):
        """Setup logging configuration"""
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Set log level
        log_level = getattr(logging, config.get('log_level', 'INFO').upper())
        self.logger.setLevel(log_level)
        
        # Create log directory
        log_dir = Path(config.get('log_dir'))
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        
        simple_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(simple_formatter)
        console_handler.setLevel(logging.INFO)
        self.logger.addHandler(console_handler)
        
        # Main log file with rotation
        main_log_file = log_dir / f"{self.name}.log"
        file_handler = logging.handlers.RotatingFileHandler(
            main_log_file,
            maxBytes=config.get('max_log_size_mb', 100) * 1024 * 1024,
            backupCount=config.get('max_log_files', 30)
        )
        file_handler.setFormatter(detailed_formatter)
        file_handler.setLevel(logging.DEBUG)
        self.logger.addHandler(file_handler)
        
        # Error log file
        error_log_file = log_dir / f"{self.name}_errors.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=50 * 1024 * 1024,  # 50MB
            backupCount=10
        )
        error_handler.setFormatter(detailed_formatter)
        error_handler.setLevel(logging.ERROR)
        self.logger.addHandler(error_handler)
        
        # JSON structured log for monitoring
        json_log_file = log_dir / f"{self.name}_structured.log"
        json_handler = logging.handlers.RotatingFileHandler(
            json_log_file,
            maxBytes=100 * 1024 * 1024,  # 100MB
            backupCount=5
        )
        json_handler.setFormatter(logging.Formatter('%(message)s'))
        json_handler.setLevel(logging.INFO)
        self.json_handler = json_handler
        self.logger.addHandler(json_handler)
        
    def _log_structured(self, level: str, message: str, **kwargs):
        """Log structured data for monitoring"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'level': level,
            'message': message,
            'component': self.name,
            **kwargs
        }
        
        # Send to JSON handler
        json_record = logging.LogRecord(
            name=self.name,
            level=getattr(logging, level.upper()),
            pathname='',
            lineno=0,
            msg=json.dumps(log_entry),
            args=(),
            exc_info=None
        )
        self.json_handler.emit(json_record)
    
    def info(self, message: str, **kwargs):
        """Log info message"""
        self.logger.info(message)
        self._log_structured('info', message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message"""
        self.logger.warning(message)
        self._log_structured('warning', message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message"""
        self.logger.error(message)
        self._log_structured('error', message, **kwargs)
        
        # Send notification for errors
        self._send_notification('error', message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message"""
        self.logger.critical(message)
        self._log_structured('critical', message, **kwargs)
        
        # Send notification for critical errors
        self._send_notification('critical', message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """Log debug message"""
        self.logger.debug(message)
        if config.get('debug', False):
            self._log_structured('debug', message, **kwargs)
    
    def exception(self, message: str, **kwargs):
        """Log exception with traceback"""
        exc_info = sys.exc_info()
        self.logger.exception(message)
        
        # Add traceback to structured log
        kwargs['traceback'] = traceback.format_exception(*exc_info)
        self._log_structured('error', message, **kwargs)
        
        # Send notification for exceptions
        self._send_notification('exception', message, **kwargs)
    
    def log_operation(self, operation: str, status: str, **kwargs):
        """Log operation with structured data"""
        message = f"Operation {operation}: {status}"
        
        operation_data = {
            'operation': operation,
            'status': status,
            'duration': kwargs.get('duration'),
            'files_processed': kwargs.get('files_processed'),
            'success_count': kwargs.get('success_count'),
            'error_count': kwargs.get('error_count'),
            **kwargs
        }
        
        if status.lower() in ['success', 'completed']:
            self.info(message, **operation_data)
        elif status.lower() in ['failed', 'error']:
            self.error(message, **operation_data)
        else:
            self.info(message, **operation_data)
    
    def log_video_processing(self, video_path: str, operation: str, status: str, **kwargs):
        """Log video processing operations"""
        video_data = {
            'video_path': video_path,
            'video_name': os.path.basename(video_path),
            'operation': operation,
            'status': status,
            'file_size_mb': kwargs.get('file_size_mb'),
            'duration_seconds': kwargs.get('duration_seconds'),
            'processing_time': kwargs.get('processing_time'),
            **kwargs
        }
        
        message = f"Video {operation}: {os.path.basename(video_path)} - {status}"
        
        if status.lower() in ['success', 'completed']:
            self.info(message, **video_data)
        elif status.lower() in ['failed', 'error']:
            self.error(message, **video_data)
        else:
            self.info(message, **video_data)
    
    def log_system_stats(self, **stats):
        """Log system statistics"""
        self.info("System statistics", **stats)
    
    def _send_notification(self, level: str, message: str, **kwargs):
        """Send notifications for important events"""
        if not config.get('enable_monitoring', False):
            return
        
        try:
            # Webhook notification
            webhook_url = config.get('webhook_url')
            if webhook_url:
                self._send_webhook_notification(webhook_url, level, message, **kwargs)
            
            # Email notification
            if config.get('email_notifications', False):
                self._send_email_notification(level, message, **kwargs)
                
        except Exception as e:
            self.logger.error(f"Failed to send notification: {e}")
    
    def _send_webhook_notification(self, webhook_url: str, level: str, message: str, **kwargs):
        """Send webhook notification"""
        import requests
        
        payload = {
            'level': level,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'component': self.name,
            'server': os.uname().nodename if hasattr(os, 'uname') else 'unknown',
            **kwargs
        }
        
        try:
            response = requests.post(webhook_url, json=payload, timeout=10)
            response.raise_for_status()
        except Exception as e:
            self.logger.error(f"Webhook notification failed: {e}")
    
    def _send_email_notification(self, level: str, message: str, **kwargs):
        """Send email notification"""
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart
        
        try:
            smtp_server = config.get('smtp_server')
            smtp_port = config.get('smtp_port', 587)
            smtp_username = config.get('smtp_username')
            smtp_password = config.get('smtp_password')
            notification_email = config.get('notification_email')
            
            if not all([smtp_server, smtp_username, smtp_password, notification_email]):
                return
            
            msg = MIMEMultipart()
            msg['From'] = smtp_username
            msg['To'] = notification_email
            msg['Subject'] = f"YouTube Pipeline Alert - {level.upper()}"
            
            body = f"""
            Alert Level: {level.upper()}
            Message: {message}
            Timestamp: {datetime.now().isoformat()}
            Component: {self.name}
            
            Additional Details:
            {json.dumps(kwargs, indent=2)}
            """
            
            msg.attach(MIMEText(body, 'plain'))
            
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(smtp_username, smtp_password)
            server.send_message(msg)
            server.quit()
            
        except Exception as e:
            self.logger.error(f"Email notification failed: {e}")

# Global logger instances
main_logger = PipelineLogger("main")
download_logger = PipelineLogger("downloader")
modify_logger = PipelineLogger("modifier")
upload_logger = PipelineLogger("uploader")
monitor_logger = PipelineLogger("monitor")

def get_logger(component: str) -> PipelineLogger:
    """Get logger for specific component"""
    loggers = {
        'main': main_logger,
        'download': download_logger,
        'modify': modify_logger,
        'upload': upload_logger,
        'monitor': monitor_logger
    }
    
    return loggers.get(component, main_logger)
