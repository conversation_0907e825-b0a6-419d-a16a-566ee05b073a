#!/bin/bash

# Isolated Installation Script for YouTube to Dailymotion Pipeline
# This script ensures complete isolation from other projects on your VPS
# Works alongside aapanel, RDP, and other existing services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration - Completely isolated paths
PROJECT_NAME="youtube-dailymotion-pipeline"
INSTALL_DIR="/opt/$PROJECT_NAME"
SERVICE_NAME="yt-dm-pipeline"
USER="yt-pipeline"
GROUP="yt-pipeline"
PYTHON_VERSION="3.11"

echo -e "${BLUE}YouTube to Dailymotion Pipeline - Isolated Installation${NC}"
echo "======================================================"
echo "This installation will NOT interfere with your existing projects:"
echo "- aapanel will continue working normally"
echo "- RDP will not be affected"
echo "- Your other Python scripts will remain untouched"
echo "- Uses completely separate user, directories, and ports"
echo ""

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}This script must be run as root for initial setup${NC}"
   echo "After setup, the pipeline will run as a separate user"
   exit 1
fi

# Function to check if port is available
check_port() {
    local port=$1
    if netstat -tuln | grep -q ":$port "; then
        return 1  # Port is in use
    else
        return 0  # Port is available
    fi
}

# Find available ports (avoiding common ones used by aapanel, RDP, etc.)
echo -e "${YELLOW}Finding available ports...${NC}"
DASHBOARD_PORT=8090
MONITORING_PORT=9091

# Check and find available dashboard port
while ! check_port $DASHBOARD_PORT; do
    DASHBOARD_PORT=$((DASHBOARD_PORT + 1))
    if [ $DASHBOARD_PORT -gt 8999 ]; then
        echo -e "${RED}Could not find available port for dashboard${NC}"
        exit 1
    fi
done

# Check and find available monitoring port
while ! check_port $MONITORING_PORT; do
    MONITORING_PORT=$((MONITORING_PORT + 1))
    if [ $MONITORING_PORT -gt 9999 ]; then
        echo -e "${RED}Could not find available port for monitoring${NC}"
        exit 1
    fi
done

echo -e "${GREEN}Using ports: Dashboard=$DASHBOARD_PORT, Monitoring=$MONITORING_PORT${NC}"

# Create dedicated user and group (completely isolated)
echo -e "${YELLOW}Creating dedicated user and group...${NC}"
if ! getent group $GROUP > /dev/null 2>&1; then
    groupadd --system $GROUP
fi

if ! getent passwd $USER > /dev/null 2>&1; then
    useradd --system --gid $GROUP --home-dir $INSTALL_DIR --shell /bin/bash $USER
fi

# Create installation directory with proper permissions
echo -e "${YELLOW}Creating isolated installation directory...${NC}"
mkdir -p $INSTALL_DIR
chown $USER:$GROUP $INSTALL_DIR
chmod 755 $INSTALL_DIR

# Install Python (isolated version if needed)
echo -e "${YELLOW}Setting up isolated Python environment...${NC}"
apt update
apt install -y software-properties-common
add-apt-repository -y ppa:deadsnakes/ppa
apt update
apt install -y python$PYTHON_VERSION python$PYTHON_VERSION-venv python$PYTHON_VERSION-dev

# Install system dependencies (minimal, non-conflicting)
echo -e "${YELLOW}Installing minimal system dependencies...${NC}"
apt install -y \
    ffmpeg \
    curl \
    wget \
    supervisor \
    logrotate

# Switch to the dedicated user for the rest of the installation
echo -e "${YELLOW}Setting up project as dedicated user...${NC}"
sudo -u $USER bash << EOF
cd $INSTALL_DIR

# Create isolated Python virtual environment
python$PYTHON_VERSION -m venv venv
source venv/bin/activate

# Upgrade pip in isolation
pip install --upgrade pip

# Create project structure
mkdir -p {downloads,modified_videos,logs,assets,config,scripts}

# Create requirements.txt
cat > requirements.txt << 'REQUIREMENTS_EOF'
# Core dependencies - isolated versions
yt-dlp>=2023.12.30
moviepy>=1.0.3
requests>=2.31.0
schedule>=1.2.0
psutil>=5.9.0
flask>=2.3.0

# Video processing
opencv-python>=4.8.0
Pillow>=10.0.0
numpy>=1.24.0

# System monitoring
python-daemon>=3.0.1
REQUIREMENTS_EOF

# Install Python dependencies in isolation
pip install -r requirements.txt

# Create isolated configuration
cat > config/config.json << 'CONFIG_EOF'
{
  "base_dir": "$INSTALL_DIR",
  "download_dir": "$INSTALL_DIR/downloads",
  "modified_dir": "$INSTALL_DIR/modified_videos",
  "log_dir": "$INSTALL_DIR/logs",
  "logo_path": "$INSTALL_DIR/assets/logo.png",
  "cookies_path": "$INSTALL_DIR/config/cookies.txt",
  "channels_file": "$INSTALL_DIR/config/channels.txt",
  "keywords_file": "$INSTALL_DIR/config/keywords.txt",
  "history_file": "$INSTALL_DIR/config/download_history.json",
  "uploaded_log": "$INSTALL_DIR/config/uploaded_videos.log",
  "token_file": "$INSTALL_DIR/config/token.json",
  
  "channel_name": "Fast in Sport",
  "dailymotion_channel": "sport",
  "dailymotion_tags": ["sports", "highlights", "fastinsport"],
  
  "speed_factor": 1.03,
  "brightness_factor": 1.08,
  "contrast_factor": 1.05,
  "logo_position": "bottom-right",
  "logo_size": 0.12,
  
  "upload_limit": 3,
  "download_limit": 5,
  "concurrent_uploads": 1,
  "max_video_size_gb": 1.5,
  "min_free_space_gb": 5.0,
  
  "run_interval_hours": 8,
  "download_schedule": "0 2,10,18 * * *",
  "upload_schedule": "30 4,12,20 * * *",
  "cleanup_schedule": "0 1 * * *",
  
  "max_retries": 2,
  "retry_delay": 300,
  "rate_limit_delay": 120,
  "health_check_interval": 600,
  
  "enable_monitoring": true,
  "log_level": "INFO",
  "max_log_files": 15,
  "max_log_size_mb": 50,
  
  "dashboard_host": "127.0.0.1",
  "dashboard_port": $DASHBOARD_PORT,
  "monitoring_port": $MONITORING_PORT
}
CONFIG_EOF

# Replace placeholder with actual install dir
sed -i "s|\$INSTALL_DIR|$INSTALL_DIR|g" config/config.json
sed -i "s|\$DASHBOARD_PORT|$DASHBOARD_PORT|g" config/config.json
sed -i "s|\$MONITORING_PORT|$MONITORING_PORT|g" config/config.json

# Create sample configuration files
cat > config/channels.txt << 'CHANNELS_EOF'
# Add YouTube channel URLs here, one per line
# Examples (uncomment and modify):
# https://www.youtube.com/@espn
# https://www.youtube.com/@skysports
# https://www.youtube.com/c/NBCSports
CHANNELS_EOF

cat > config/keywords.txt << 'KEYWORDS_EOF'
# Add keywords to filter videos, one per line
highlights
goals
best moments
match
game
sport
football
soccer
basketball
tennis
KEYWORDS_EOF

# Create empty files
touch config/cookies.txt
echo '{}' > config/download_history.json
touch config/uploaded_videos.log

# Set secure permissions
chmod 600 config/cookies.txt config/token.json 2>/dev/null || true
chmod 644 config/config.json config/channels.txt config/keywords.txt

# Create startup script
cat > scripts/start.sh << 'START_EOF'
#!/bin/bash
cd $INSTALL_DIR
source venv/bin/activate
export CONFIG_FILE=$INSTALL_DIR/config/config.json
python daemon.py start
START_EOF

cat > scripts/stop.sh << 'STOP_EOF'
#!/bin/bash
cd $INSTALL_DIR
source venv/bin/activate
export CONFIG_FILE=$INSTALL_DIR/config/config.json
python daemon.py stop
STOP_EOF

cat > scripts/status.sh << 'STATUS_EOF'
#!/bin/bash
cd $INSTALL_DIR
source venv/bin/activate
export CONFIG_FILE=$INSTALL_DIR/config/config.json
python daemon.py status
STATUS_EOF

chmod +x scripts/*.sh

# Replace placeholders in scripts
sed -i "s|\$INSTALL_DIR|$INSTALL_DIR|g" scripts/*.sh

echo "User setup completed successfully"
EOF

# Create systemd service (isolated)
echo -e "${YELLOW}Creating isolated systemd service...${NC}"
cat > /etc/systemd/system/$SERVICE_NAME.service << EOF
[Unit]
Description=YouTube to Dailymotion Pipeline (Isolated)
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=$USER
Group=$GROUP
WorkingDirectory=$INSTALL_DIR
Environment=PATH=$INSTALL_DIR/venv/bin:/usr/local/bin:/usr/bin:/bin
Environment=CONFIG_FILE=$INSTALL_DIR/config/config.json
Environment=PYTHONPATH=$INSTALL_DIR
ExecStart=$INSTALL_DIR/venv/bin/python daemon.py start
ExecStop=$INSTALL_DIR/venv/bin/python daemon.py stop
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=30
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

# Resource limits (to not interfere with other services)
MemoryLimit=2G
CPUQuota=150%
TasksMax=100

# Security settings (complete isolation)
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$INSTALL_DIR
PrivateDevices=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true

[Install]
WantedBy=multi-user.target
EOF

# Create isolated log rotation
echo -e "${YELLOW}Setting up isolated log rotation...${NC}"
cat > /etc/logrotate.d/$SERVICE_NAME << EOF
$INSTALL_DIR/logs/*.log {
    daily
    missingok
    rotate 15
    compress
    delaycompress
    notifempty
    create 644 $USER $GROUP
    su $USER $GROUP
    postrotate
        systemctl reload $SERVICE_NAME 2>/dev/null || true
    endscript
}
EOF

# Create monitoring script (isolated)
echo -e "${YELLOW}Creating isolated monitoring...${NC}"
cat > $INSTALL_DIR/scripts/monitor.sh << EOF
#!/bin/bash

# Isolated monitoring script
INSTALL_DIR="$INSTALL_DIR"
LOG_FILE="\$INSTALL_DIR/logs/monitor.log"
SERVICE_NAME="$SERVICE_NAME"

# Function to log with timestamp
log() {
    echo "\$(date '+%Y-%m-%d %H:%M:%S') - \$1" >> "\$LOG_FILE"
}

# Check if service is running
if ! systemctl is-active --quiet \$SERVICE_NAME; then
    log "WARNING: Pipeline service is not running, attempting restart"
    systemctl restart \$SERVICE_NAME
    sleep 15
    
    if systemctl is-active --quiet \$SERVICE_NAME; then
        log "INFO: Pipeline service restarted successfully"
    else
        log "ERROR: Failed to restart pipeline service"
    fi
else
    log "INFO: Pipeline service is running normally"
fi

# Check disk space (only for our directory)
DISK_USAGE=\$(df "\$INSTALL_DIR" | awk 'NR==2 {print \$5}' | sed 's/%//')
if [ "\$DISK_USAGE" -gt 80 ]; then
    log "WARNING: Pipeline disk usage is high: \${DISK_USAGE}%"
fi

log "INFO: Health check completed - Pipeline disk usage: \${DISK_USAGE}%"
EOF

chmod +x $INSTALL_DIR/scripts/monitor.sh
chown $USER:$GROUP $INSTALL_DIR/scripts/monitor.sh

# Set up isolated cron job
echo -e "${YELLOW}Setting up isolated monitoring cron...${NC}"
sudo -u $USER bash << EOF
# Add cron job for monitoring (every 10 minutes)
(crontab -l 2>/dev/null; echo "*/10 * * * * $INSTALL_DIR/scripts/monitor.sh") | crontab -
EOF

# Create management scripts for easy control
echo -e "${YELLOW}Creating management scripts...${NC}"
cat > /usr/local/bin/yt-pipeline << EOF
#!/bin/bash
# Management script for YouTube to Dailymotion Pipeline

case "\$1" in
    start)
        echo "Starting YouTube to Dailymotion Pipeline..."
        systemctl start $SERVICE_NAME
        ;;
    stop)
        echo "Stopping YouTube to Dailymotion Pipeline..."
        systemctl stop $SERVICE_NAME
        ;;
    restart)
        echo "Restarting YouTube to Dailymotion Pipeline..."
        systemctl restart $SERVICE_NAME
        ;;
    status)
        systemctl status $SERVICE_NAME
        echo ""
        sudo -u $USER $INSTALL_DIR/scripts/status.sh
        ;;
    logs)
        journalctl -u $SERVICE_NAME -f
        ;;
    config)
        echo "Configuration file: $INSTALL_DIR/config/config.json"
        echo "Channels file: $INSTALL_DIR/config/channels.txt"
        echo "Keywords file: $INSTALL_DIR/config/keywords.txt"
        echo "Cookies file: $INSTALL_DIR/config/cookies.txt"
        ;;
    dashboard)
        echo "Dashboard URL: http://localhost:$DASHBOARD_PORT"
        echo "Monitoring URL: http://localhost:$MONITORING_PORT"
        ;;
    *)
        echo "Usage: yt-pipeline {start|stop|restart|status|logs|config|dashboard}"
        exit 1
        ;;
esac
EOF

chmod +x /usr/local/bin/yt-pipeline

# Enable service but don't start yet
echo -e "${YELLOW}Enabling service...${NC}"
systemctl daemon-reload
systemctl enable $SERVICE_NAME

# Final permissions check
chown -R $USER:$GROUP $INSTALL_DIR
chmod -R 755 $INSTALL_DIR
chmod 600 $INSTALL_DIR/config/cookies.txt $INSTALL_DIR/config/token.json 2>/dev/null || true

echo -e "${GREEN}Installation completed successfully!${NC}"
echo ""
echo -e "${BLUE}=== ISOLATION SUMMARY ===${NC}"
echo "✅ Dedicated user: $USER (completely isolated)"
echo "✅ Dedicated directory: $INSTALL_DIR"
echo "✅ Dedicated service: $SERVICE_NAME"
echo "✅ Isolated Python environment with own packages"
echo "✅ Resource limits: 2GB RAM, 150% CPU"
echo "✅ Dashboard port: $DASHBOARD_PORT (isolated)"
echo "✅ Monitoring port: $MONITORING_PORT (isolated)"
echo "✅ No interference with aapanel, RDP, or other projects"
echo ""
echo -e "${BLUE}=== NEXT STEPS ===${NC}"
echo "1. Configure your settings:"
echo "   - Edit: $INSTALL_DIR/config/config.json"
echo "   - Add channels: $INSTALL_DIR/config/channels.txt"
echo "   - Add cookies: $INSTALL_DIR/config/cookies.txt"
echo ""
echo "2. Test the configuration:"
echo "   sudo -u $USER bash -c 'cd $INSTALL_DIR && source venv/bin/activate && python daemon.py test'"
echo ""
echo "3. Start the service:"
echo "   yt-pipeline start"
echo ""
echo -e "${BLUE}=== MANAGEMENT COMMANDS ===${NC}"
echo "yt-pipeline start     - Start the pipeline"
echo "yt-pipeline stop      - Stop the pipeline"
echo "yt-pipeline status    - Check status"
echo "yt-pipeline logs      - View logs"
echo "yt-pipeline config    - Show config file locations"
echo "yt-pipeline dashboard - Show dashboard URLs"
echo ""
echo -e "${YELLOW}Your existing projects (aapanel, RDP, etc.) are completely unaffected!${NC}"
