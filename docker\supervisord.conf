[supervisord]
nodaemon=true
user=pipeline
logfile=/app/logs/supervisord.log
pidfile=/app/logs/supervisord.pid

[program:youtube-pipeline]
command=python daemon.py start
directory=/app
user=pipeline
autostart=true
autorestart=true
startretries=3
redirect_stderr=true
stdout_logfile=/app/logs/pipeline.log
stdout_logfile_maxbytes=100MB
stdout_logfile_backups=5

[program:cron]
command=cron -f
user=root
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/cron.log

[unix_http_server]
file=/tmp/supervisor.sock
chmod=0700
chown=pipeline:pipeline

[supervisorctl]
serverurl=unix:///tmp/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface
