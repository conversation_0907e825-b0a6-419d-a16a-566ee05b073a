# YouTube to Dailymotion Pipeline Docker Image
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive
ENV CONFIG_FILE=/app/config/config.json

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    wget \
    curl \
    git \
    cron \
    supervisor \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Create non-root user
RUN useradd -m -u 1000 pipeline && \
    chown -R pipeline:pipeline /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application files
COPY . .

# Create necessary directories
RUN mkdir -p /app/{downloads,modified_videos,logs,assets,config} && \
    chown -R pipeline:pipeline /app

# Copy supervisor configuration
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Copy entrypoint script
COPY docker/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# Set up cron for monitoring
COPY docker/crontab /etc/cron.d/pipeline-cron
RUN chmod 0644 /etc/cron.d/pipeline-cron && \
    crontab -u pipeline /etc/cron.d/pipeline-cron

# Switch to non-root user
USER pipeline

# Expose ports (if needed for monitoring)
EXPOSE 8080

# Health check
HEALTHCHECK --interval=5m --timeout=30s --start-period=1m --retries=3 \
    CMD python daemon.py status || exit 1

# Set entrypoint
ENTRYPOINT ["/entrypoint.sh"]
CMD ["start"]
