"""
Configuration management for YouTube to Dailymotion pipeline
"""
import os
import json
from pathlib import Path
from typing import Dict, Any

class Config:
    """Configuration manager for the pipeline"""
    
    def __init__(self, config_file: str = None):
        self.config_file = config_file or os.getenv('CONFIG_FILE', 'config.json')
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file or environment variables"""
        config = {}
        
        # Try to load from config file first
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
            except Exception as e:
                print(f"Warning: Could not load config file {self.config_file}: {e}")
        
        # Override with environment variables
        config.update(self._get_env_config())
        
        # Set defaults for missing values
        config = self._set_defaults(config)
        
        return config
    
    def _get_env_config(self) -> Dict[str, Any]:
        """Get configuration from environment variables"""
        return {
            # Paths
            'base_dir': os.getenv('BASE_DIR', '/home/<USER>/youtube_pipeline'),
            'download_dir': os.getenv('DOWNLOAD_DIR', '/home/<USER>/youtube_pipeline/downloads'),
            'modified_dir': os.getenv('MODIFIED_DIR', '/home/<USER>/youtube_pipeline/modified_videos'),
            'logo_path': os.getenv('LOGO_PATH', '/home/<USER>/youtube_pipeline/assets/logo.png'),
            'cookies_path': os.getenv('COOKIES_PATH', '/home/<USER>/youtube_pipeline/cookies.txt'),
            'channels_file': os.getenv('CHANNELS_FILE', '/home/<USER>/youtube_pipeline/channels.txt'),
            'keywords_file': os.getenv('KEYWORDS_FILE', '/home/<USER>/youtube_pipeline/keywords.txt'),
            'history_file': os.getenv('HISTORY_FILE', '/home/<USER>/youtube_pipeline/download_history.json'),
            'uploaded_log': os.getenv('UPLOADED_LOG', '/home/<USER>/youtube_pipeline/uploaded_videos.log'),
            'log_dir': os.getenv('LOG_DIR', '/home/<USER>/youtube_pipeline/logs'),
            
            # Channel settings
            'channel_name': os.getenv('CHANNEL_NAME', 'Fast in Sport'),
            'dailymotion_channel': os.getenv('DAILYMOTION_CHANNEL', 'sport'),
            'dailymotion_tags': os.getenv('DAILYMOTION_TAGS', 'sports,highlights,fastinsport').split(','),
            
            # Video modification settings
            'speed_factor': float(os.getenv('SPEED_FACTOR', '1.03')),
            'brightness_factor': float(os.getenv('BRIGHTNESS_FACTOR', '1.08')),
            'contrast_factor': float(os.getenv('CONTRAST_FACTOR', '1.05')),
            'logo_position': os.getenv('LOGO_POSITION', 'bottom-right'),
            'logo_size': float(os.getenv('LOGO_SIZE', '0.12')),
            
            # Processing limits
            'upload_limit': int(os.getenv('UPLOAD_LIMIT', '5')),
            'download_limit': int(os.getenv('DOWNLOAD_LIMIT', '10')),
            'concurrent_uploads': int(os.getenv('CONCURRENT_UPLOADS', '1')),
            'max_video_size_gb': float(os.getenv('MAX_VIDEO_SIZE_GB', '2.0')),
            'min_free_space_gb': float(os.getenv('MIN_FREE_SPACE_GB', '10.0')),
            
            # Scheduling
            'run_interval_hours': int(os.getenv('RUN_INTERVAL_HOURS', '6')),
            'download_schedule': os.getenv('DOWNLOAD_SCHEDULE', '0 */6 * * *'),  # Every 6 hours
            'upload_schedule': os.getenv('UPLOAD_SCHEDULE', '30 */3 * * *'),    # Every 3 hours, offset by 30 min
            'cleanup_schedule': os.getenv('CLEANUP_SCHEDULE', '0 2 * * *'),     # Daily at 2 AM
            
            # Safety settings
            'max_retries': int(os.getenv('MAX_RETRIES', '3')),
            'retry_delay': int(os.getenv('RETRY_DELAY', '300')),  # 5 minutes
            'rate_limit_delay': int(os.getenv('RATE_LIMIT_DELAY', '60')),  # 1 minute between operations
            'health_check_interval': int(os.getenv('HEALTH_CHECK_INTERVAL', '300')),  # 5 minutes
            
            # Dailymotion API
            'dailymotion_client_id': os.getenv('DAILYMOTION_CLIENT_ID'),
            'dailymotion_client_secret': os.getenv('DAILYMOTION_CLIENT_SECRET'),
            'dailymotion_username': os.getenv('DAILYMOTION_USERNAME'),
            'dailymotion_password': os.getenv('DAILYMOTION_PASSWORD'),
            'token_file': os.getenv('TOKEN_FILE', '/home/<USER>/youtube_pipeline/token.json'),
            
            # Monitoring
            'enable_monitoring': os.getenv('ENABLE_MONITORING', 'true').lower() == 'true',
            'webhook_url': os.getenv('WEBHOOK_URL'),  # For notifications
            'email_notifications': os.getenv('EMAIL_NOTIFICATIONS', 'false').lower() == 'true',
            'smtp_server': os.getenv('SMTP_SERVER'),
            'smtp_port': int(os.getenv('SMTP_PORT', '587')),
            'smtp_username': os.getenv('SMTP_USERNAME'),
            'smtp_password': os.getenv('SMTP_PASSWORD'),
            'notification_email': os.getenv('NOTIFICATION_EMAIL'),
        }
    
    def _set_defaults(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Set default values for missing configuration"""
        defaults = {
            'debug': False,
            'dry_run': False,
            'log_level': 'INFO',
            'max_log_files': 30,
            'max_log_size_mb': 100,
        }
        
        for key, value in defaults.items():
            if key not in config:
                config[key] = value
                
        return config
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        return self.config.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """Set configuration value"""
        self.config[key] = value
    
    def save(self) -> None:
        """Save current configuration to file"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def create_directories(self) -> None:
        """Create necessary directories"""
        dirs_to_create = [
            'base_dir', 'download_dir', 'modified_dir', 'log_dir'
        ]
        
        for dir_key in dirs_to_create:
            dir_path = self.get(dir_key)
            if dir_path:
                Path(dir_path).mkdir(parents=True, exist_ok=True)
                print(f"Created directory: {dir_path}")
    
    def validate(self) -> bool:
        """Validate configuration"""
        required_keys = [
            'base_dir', 'download_dir', 'modified_dir', 'log_dir',
            'dailymotion_client_id', 'dailymotion_client_secret',
            'dailymotion_username', 'dailymotion_password'
        ]
        
        missing_keys = []
        for key in required_keys:
            if not self.get(key):
                missing_keys.append(key)
        
        if missing_keys:
            print(f"Missing required configuration keys: {missing_keys}")
            return False
        
        return True
    
    def get_disk_usage(self, path: str) -> Dict[str, float]:
        """Get disk usage information in GB"""
        try:
            stat = os.statvfs(path)
            total = (stat.f_blocks * stat.f_frsize) / (1024**3)
            free = (stat.f_bavail * stat.f_frsize) / (1024**3)
            used = total - free
            
            return {
                'total_gb': round(total, 2),
                'used_gb': round(used, 2),
                'free_gb': round(free, 2),
                'usage_percent': round((used / total) * 100, 2)
            }
        except Exception as e:
            print(f"Error getting disk usage for {path}: {e}")
            return {'total_gb': 0, 'used_gb': 0, 'free_gb': 0, 'usage_percent': 0}
    
    def check_disk_space(self) -> bool:
        """Check if there's enough free disk space"""
        base_dir = self.get('base_dir')
        min_free_space = self.get('min_free_space_gb', 10.0)
        
        disk_info = self.get_disk_usage(base_dir)
        return disk_info['free_gb'] >= min_free_space

# Global config instance
config = Config()
