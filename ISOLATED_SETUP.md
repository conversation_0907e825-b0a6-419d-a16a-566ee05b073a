# YouTube to Dailymotion Pipeline - Isolated VPS Setup

## 🎯 **Perfect for Multi-Project VPS**

This setup is specifically designed for VPS environments where you already have:
- **aapanel** (or other control panels)
- **RDP** services
- **Other Python scripts**
- **Web servers, databases, etc.**

The pipeline will **NOT interfere** with your existing projects and will automatically pause when other services need resources.

## 🔒 **Complete Isolation Features**

### ✅ **What's Isolated:**
- **Dedicated User**: `yt-pipeline` (separate from your main user)
- **Dedicated Directory**: `/opt/youtube-dailymotion-pipeline`
- **Isolated Python Environment**: Own virtual environment with packages
- **Separate Ports**: Auto-detects and avoids your existing services
- **Resource Limits**: Maximum 40% memory, 60% CPU usage
- **Lower Priority**: Runs with reduced system priority
- **Separate Service**: `yt-dm-pipeline` systemd service

### ✅ **What's Protected:**
- Your **aapanel** installation
- Your **RDP** service
- Your **existing Python scripts**
- Your **web servers** (nginx, apache)
- Your **databases** (MySQL, PostgreSQL)
- Your **other applications**

## 🚀 **Quick Installation**

### **Step 1: Download and Run Installer**
```bash
# Download the isolated installer
wget https://raw.githubusercontent.com/your-repo/isolated_install.sh
chmod +x isolated_install.sh

# Run as root (only for initial setup)
sudo ./isolated_install.sh
```

### **Step 2: Configure the Pipeline**
```bash
# Edit main configuration
sudo nano /opt/youtube-dailymotion-pipeline/config/config.json

# Add YouTube channels to monitor
sudo nano /opt/youtube-dailymotion-pipeline/config/channels.txt

# Add keywords for filtering videos
sudo nano /opt/youtube-dailymotion-pipeline/config/keywords.txt

# Add YouTube cookies (for downloading)
sudo nano /opt/youtube-dailymotion-pipeline/config/cookies.txt
```

### **Step 3: Test and Start**
```bash
# Test configuration
python /opt/youtube-dailymotion-pipeline/manage.py test

# Start the pipeline
yt-pipeline start

# Check status
yt-pipeline status
```

## ⚙️ **Configuration**

### **Main Config (`config.json`)**
```json
{
  "base_dir": "/opt/youtube-dailymotion-pipeline",
  "channel_name": "Your Channel Name",
  
  "dailymotion_client_id": "your_client_id",
  "dailymotion_client_secret": "your_client_secret",
  "dailymotion_username": "your_username",
  "dailymotion_password": "your_password",
  
  "max_memory_usage_percent": 40,
  "max_cpu_usage_percent": 60,
  "upload_limit": 3,
  "download_limit": 5,
  "max_video_size_gb": 1.0,
  
  "download_schedule": "0 2,10,18 * * *",
  "upload_schedule": "30 4,12,20 * * *",
  "cleanup_schedule": "0 1 * * *",
  
  "dashboard_port": 8090,
  "auto_pause_on_high_load": true
}
```

### **Channels (`channels.txt`)**
```
https://www.youtube.com/@espn
https://www.youtube.com/@skysports
https://www.youtube.com/c/NBCSports
```

### **Keywords (`keywords.txt`)**
```
highlights
goals
best moments
match
sport
football
soccer
basketball
```

## 🎮 **Management Commands**

### **Service Control**
```bash
yt-pipeline start      # Start the pipeline
yt-pipeline stop       # Stop the pipeline
yt-pipeline restart    # Restart the pipeline
yt-pipeline status     # Show detailed status
```

### **Operation Control**
```bash
yt-pipeline pause      # Pause operations (keep service running)
yt-pipeline resume     # Resume operations
yt-pipeline logs       # Show recent logs
yt-pipeline test       # Test configuration
```

### **Advanced Management**
```bash
# Using the Python manager
python /opt/youtube-dailymotion-pipeline/manage.py status
python /opt/youtube-dailymotion-pipeline/manage.py logs --lines 50

# Direct systemd control
sudo systemctl status yt-dm-pipeline
sudo journalctl -u yt-dm-pipeline -f
```

## 📊 **Monitoring**

### **Web Dashboard**
- **URL**: `http://your-vps-ip:8090` (or configured port)
- **Features**: Real-time stats, resource usage, logs
- **Access**: Localhost only by default (secure)

### **Resource Monitoring**
The pipeline automatically monitors:
- **CPU Usage**: Pauses if system CPU > 60%
- **Memory Usage**: Pauses if system memory > 40% of our limit
- **Disk Space**: Stops if free space < 5GB
- **Other Processes**: Detects high-priority processes and yields

### **Log Files**
```bash
# Main logs
tail -f /opt/youtube-dailymotion-pipeline/logs/main.log

# Error logs
tail -f /opt/youtube-dailymotion-pipeline/logs/main_errors.log

# Monitor logs
tail -f /opt/youtube-dailymotion-pipeline/logs/monitor.log
```

## 🔧 **Resource Management**

### **Automatic Resource Control**
- **CPU Priority**: Runs with `nice +10` (lower priority)
- **I/O Priority**: Uses idle I/O class when available
- **Memory Limit**: Systemd limits to 2GB maximum
- **CPU Limit**: Systemd limits to 150% (1.5 cores)

### **Smart Pausing**
The pipeline automatically pauses when it detects:
- High CPU usage from other processes
- High memory usage
- Database operations (MySQL, PostgreSQL)
- Web server activity (nginx, apache)
- Backup operations
- aapanel operations

### **Manual Control**
```bash
# Pause during your important work
yt-pipeline pause

# Resume when done
yt-pipeline resume

# Check what's using resources
yt-pipeline status
```

## 🛡️ **Safety Features**

### **Isolation Guarantees**
- ✅ **Separate User**: Cannot access your files
- ✅ **Separate Directory**: Contained in `/opt/youtube-dailymotion-pipeline`
- ✅ **Resource Limits**: Cannot consume all system resources
- ✅ **Port Isolation**: Automatically finds unused ports
- ✅ **Process Isolation**: Lower priority, can be easily stopped

### **Automatic Safety**
- ✅ **Auto-pause** on high system load
- ✅ **Auto-cleanup** of old files
- ✅ **Error recovery** with retries
- ✅ **Graceful shutdown** on system signals
- ✅ **Health monitoring** every 10 minutes

## 🔍 **Troubleshooting**

### **Common Issues**

1. **Service won't start**:
```bash
# Check configuration
python /opt/youtube-dailymotion-pipeline/manage.py test

# Check logs
sudo journalctl -u yt-dm-pipeline -n 20
```

2. **High resource usage**:
```bash
# Check status
yt-pipeline status

# Manually pause
yt-pipeline pause
```

3. **Port conflicts**:
```bash
# Check what's using ports
netstat -tuln | grep 8090

# The installer automatically finds free ports
```

4. **Permission issues**:
```bash
# Fix permissions
sudo chown -R yt-pipeline:yt-pipeline /opt/youtube-dailymotion-pipeline
```

### **Getting Help**
```bash
# Detailed status
yt-pipeline status

# Recent logs
yt-pipeline logs

# Test everything
python /opt/youtube-dailymotion-pipeline/manage.py test
```

## 📈 **Performance Tips**

### **For Busy VPS**
- Set `max_memory_usage_percent` to 30% or lower
- Set `max_cpu_usage_percent` to 50% or lower
- Increase `rate_limit_delay` to 300 seconds
- Reduce `upload_limit` to 2 or 1

### **For Dedicated Resources**
- Increase limits if you have spare resources
- Adjust schedules to run during low-traffic hours
- Monitor dashboard for optimal settings

## 🔄 **Updates**

### **Updating the Pipeline**
```bash
# Stop the service
yt-pipeline stop

# Update files (git pull or download new files)
cd /opt/youtube-dailymotion-pipeline
sudo -u yt-pipeline git pull

# Restart
yt-pipeline start
```

## ✅ **Verification Checklist**

After installation, verify:
- [ ] Service is running: `yt-pipeline status`
- [ ] No port conflicts: `netstat -tuln | grep 8090`
- [ ] Resources are reasonable: Check CPU/memory in `yt-pipeline status`
- [ ] Your other services still work: Check aapanel, RDP, etc.
- [ ] Logs are clean: `yt-pipeline logs`
- [ ] Dashboard accessible: `http://localhost:8090`

## 🎉 **Success!**

Your YouTube to Dailymotion pipeline is now running in complete isolation alongside your existing VPS projects. It will:

- ✅ **Run 24/7** without interfering with other services
- ✅ **Automatically pause** when other services need resources  
- ✅ **Monitor itself** and recover from errors
- ✅ **Stay out of the way** of your main work
- ✅ **Provide monitoring** through web dashboard and logs

Your **aapanel**, **RDP**, and **other projects** will continue working exactly as before!
