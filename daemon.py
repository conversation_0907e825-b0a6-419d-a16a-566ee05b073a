#!/usr/bin/env python3
"""
24/7 Daemon for YouTube to Dailymotion pipeline
"""
import os
import sys
import argparse
import signal
import time
from datetime import datetime
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import config
from logger import get_logger
from scheduler import scheduler
from pipeline_manager import pipeline_manager

logger = get_logger('daemon')

class PipelineDaemon:
    """Main daemon for 24/7 pipeline operation"""
    
    def __init__(self):
        self.running = False
        self.setup_signal_handlers()
        
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        if hasattr(signal, 'SIGHUP'):
            signal.signal(signal.SIGHUP, self._reload_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.stop()
    
    def _reload_handler(self, signum, frame):
        """Handle reload signal (SIGHUP)"""
        logger.info("Received SIGHUP, reloading configuration...")
        try:
            # Reload configuration
            config._load_config()
            logger.info("Configuration reloaded successfully")
        except Exception as e:
            logger.error(f"Failed to reload configuration: {e}")
    
    def setup_jobs(self):
        """Setup scheduled jobs"""
        logger.info("Setting up scheduled jobs...")
        
        try:
            # Download job - every 6 hours
            scheduler.add_job(
                name="download_videos",
                func=pipeline_manager.download_videos,
                schedule_expr=config.get('download_schedule', '0 */6 * * *')
            )
            
            # Modify videos job - every 3 hours (offset)
            scheduler.add_job(
                name="modify_videos", 
                func=pipeline_manager.modify_videos,
                schedule_expr="30 */3 * * *"
            )
            
            # Upload videos job - every 2 hours (offset)
            scheduler.add_job(
                name="upload_videos",
                func=pipeline_manager.upload_videos,
                schedule_expr="15 */2 * * *"
            )
            
            # Full pipeline job - daily at 2 AM
            scheduler.add_job(
                name="full_pipeline",
                func=pipeline_manager.run_full_pipeline,
                schedule_expr="0 2 * * *"
            )
            
            # Cleanup job - daily at 3 AM
            scheduler.add_job(
                name="cleanup",
                func=pipeline_manager.cleanup_old_files,
                schedule_expr="0 3 * * *"
            )
            
            logger.info("Scheduled jobs setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup jobs: {e}")
            raise
    
    def start(self):
        """Start the daemon"""
        if self.running:
            logger.warning("Daemon is already running")
            return
        
        logger.info("Starting YouTube to Dailymotion pipeline daemon...")
        logger.info(f"Process ID: {os.getpid()}")
        logger.info(f"Configuration: {config.config_file}")
        
        try:
            # Validate configuration
            if not config.validate():
                logger.error("Configuration validation failed")
                return False
            
            # Check prerequisites
            if not pipeline_manager.check_prerequisites():
                logger.error("Prerequisites check failed")
                return False
            
            # Setup scheduled jobs
            self.setup_jobs()
            
            # Create PID file
            self._create_pid_file()
            
            # Start the scheduler
            self.running = True
            logger.info("Daemon started successfully")
            
            # Log startup statistics
            stats = pipeline_manager.get_stats()
            logger.info(f"Pipeline statistics: {stats}")
            
            # Start scheduler (this will block)
            scheduler.start()
            
        except Exception as e:
            logger.error(f"Failed to start daemon: {e}")
            logger.exception("Daemon startup exception")
            return False
        finally:
            self._cleanup()
        
        return True
    
    def stop(self):
        """Stop the daemon"""
        if not self.running:
            return
        
        logger.info("Stopping daemon...")
        self.running = False
        
        # Stop scheduler
        scheduler.stop()
        
        # Cleanup
        self._cleanup()
        
        logger.info("Daemon stopped")
    
    def _create_pid_file(self):
        """Create PID file"""
        try:
            pid_file = Path(config.get('base_dir')) / 'daemon.pid'
            with open(pid_file, 'w') as f:
                f.write(str(os.getpid()))
            logger.info(f"Created PID file: {pid_file}")
        except Exception as e:
            logger.warning(f"Failed to create PID file: {e}")
    
    def _cleanup(self):
        """Cleanup resources"""
        try:
            # Remove PID file
            pid_file = Path(config.get('base_dir')) / 'daemon.pid'
            if pid_file.exists():
                pid_file.unlink()
                logger.info("Removed PID file")
        except Exception as e:
            logger.warning(f"Cleanup error: {e}")
    
    def status(self):
        """Get daemon status"""
        pid_file = Path(config.get('base_dir')) / 'daemon.pid'
        
        if not pid_file.exists():
            print("Daemon is not running (no PID file)")
            return False
        
        try:
            with open(pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            # Check if process is running
            try:
                os.kill(pid, 0)  # Send signal 0 to check if process exists
                print(f"Daemon is running (PID: {pid})")
                
                # Get scheduler status
                status = scheduler.get_status()
                print(f"Scheduler status: {status}")
                
                return True
            except OSError:
                print(f"Daemon PID file exists but process {pid} is not running")
                pid_file.unlink()  # Remove stale PID file
                return False
                
        except Exception as e:
            print(f"Error checking daemon status: {e}")
            return False

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='YouTube to Dailymotion Pipeline Daemon')
    parser.add_argument('command', choices=['start', 'stop', 'restart', 'status', 'test'],
                       help='Daemon command')
    parser.add_argument('--config', '-c', help='Configuration file path')
    parser.add_argument('--debug', '-d', action='store_true', help='Enable debug mode')
    parser.add_argument('--dry-run', action='store_true', help='Dry run mode (no actual operations)')
    
    args = parser.parse_args()
    
    # Set configuration file if provided
    if args.config:
        config.config_file = args.config
        config.config = config._load_config()
    
    # Set debug mode
    if args.debug:
        config.set('debug', True)
        config.set('log_level', 'DEBUG')
    
    # Set dry run mode
    if args.dry_run:
        config.set('dry_run', True)
        logger.info("Running in dry-run mode")
    
    daemon = PipelineDaemon()
    
    if args.command == 'start':
        if daemon.status():
            print("Daemon is already running")
            sys.exit(1)
        
        print("Starting daemon...")
        success = daemon.start()
        sys.exit(0 if success else 1)
        
    elif args.command == 'stop':
        if not daemon.status():
            print("Daemon is not running")
            sys.exit(1)
        
        print("Stopping daemon...")
        daemon.stop()
        
    elif args.command == 'restart':
        print("Restarting daemon...")
        if daemon.status():
            daemon.stop()
            time.sleep(2)
        
        success = daemon.start()
        sys.exit(0 if success else 1)
        
    elif args.command == 'status':
        daemon.status()
        
    elif args.command == 'test':
        print("Running pipeline test...")
        try:
            # Test configuration
            if not config.validate():
                print("❌ Configuration validation failed")
                sys.exit(1)
            print("✅ Configuration is valid")
            
            # Test prerequisites
            if not pipeline_manager.check_prerequisites():
                print("❌ Prerequisites check failed")
                sys.exit(1)
            print("✅ Prerequisites check passed")
            
            # Test pipeline components
            print("\nTesting pipeline components...")
            
            # Test download (dry run)
            print("Testing download component...")
            result = pipeline_manager.download_videos()
            if result.get('success'):
                print("✅ Download component test passed")
            else:
                print(f"⚠️  Download component test failed: {result.get('error')}")
            
            print("✅ Pipeline test completed")
            
        except Exception as e:
            print(f"❌ Pipeline test failed: {e}")
            sys.exit(1)

if __name__ == '__main__':
    main()
