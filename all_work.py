import os
import sys
import json
import time
from datetime import datetime
import subprocess
import importlib.util

# Load modules
def load_module(module_name, file_path):
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module

# Paths to the scripts
YOUTUBE_DOWNLOADER_PATH = "youtube_downloader.py"
MODIF_VIDEO_PATH = "modif_video.py"
DAILYMOTION_UPLOADER_PATH = "dailymotion_uploader.py"

# Configuration - update these values for your setup
DOWNLOAD_DIR = "D:/youtube acript/dailymotion to youtube/downloads"  # Directory where videos are downloaded
MODIFIED_DIR = "D:/youtube acript/dailymotion to youtube/modified_videos"  # Directory for modified videos
LOGO_PATH = "D:/youtube acript/dailymotion to youtube/video file/fast in sport logo.png"  # Path to your logo
CHANNEL_NAME = "Fast in Sport"  # Your channel name

# Video modification settings
SPEED_FACTOR = 1.03
BRIGHTNESS_FACTOR = 1.08
CONTRAST_FACTOR = 1.05
LOGO_POSITION = 'bottom-right'
LOGO_SIZE = 0.12

# Dailymotion upload settings
DAILYMOTION_CHANNEL = "sport"
DAILYMOTION_TAGS = ["sports", "highlights", "fastinsport"]
UPLOAD_LIMIT = 5  # Maximum videos to upload per run

def main():
    print(f"=== Starting All-in-One YouTube Download, Modify, and Upload Process ===")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Step 1: Download videos using youtube_downloader.py
    print("\n=== STEP 1: DOWNLOADING VIDEOS ===")
    try:
        # Load the youtube_downloader module
        yt_downloader = load_module("youtube_downloader", YOUTUBE_DOWNLOADER_PATH)
        
        # Create output directory if it doesn't exist
        os.makedirs(DOWNLOAD_DIR, exist_ok=True)
        
        # Run the download function
        print("Checking channels for new videos...")
        yt_downloader.check_channels_for_new_videos()
        
        print("Download process completed.")
    except Exception as e:
        print(f"Error during download process: {e}")
        return
    
    # Step 2: Modify downloaded videos using modif_video.py
    print("\n=== STEP 2: MODIFYING VIDEOS ===")
    try:
        # Load the modif_video module
        modif_video = load_module("modif_video", MODIF_VIDEO_PATH)
        
        # Create output directory if it doesn't exist
        os.makedirs(MODIFIED_DIR, exist_ok=True)
        
        # Find all videos in the download directory
        videos_to_process = []
        for root, dirs, files in os.walk(DOWNLOAD_DIR):
            for file in files:
                if file.lower().endswith(('.mp4', '.mkv', '.avi', '.mov', '.flv', '.webm')):
                    videos_to_process.append(os.path.join(root, file))
        
        if not videos_to_process:
            print("No videos found to modify.")
            return
        
        print(f"Found {len(videos_to_process)} videos to modify.")
        
        # Process each video
        for i, video_path in enumerate(videos_to_process):
            print(f"\nProcessing video {i+1}/{len(videos_to_process)}: {video_path}")
            
            # Create output path with same directory structure
            rel_path = os.path.relpath(video_path, DOWNLOAD_DIR)
            output_path = os.path.join(MODIFIED_DIR, rel_path)
            
            # Create parent directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Modify the video
            success = modif_video.modify_video(
                video_path, 
                output_path,
                LOGO_PATH,
                CHANNEL_NAME,
                SPEED_FACTOR,
                BRIGHTNESS_FACTOR,
                CONTRAST_FACTOR,
                LOGO_POSITION,
                LOGO_SIZE
            )
            
            if success:
                print(f"Successfully modified: {video_path} -> {output_path}")
            else:
                print(f"Failed to modify: {video_path}")
        
        print("\nVideo modification process completed.")
    except Exception as e:
        print(f"Error during modification process: {e}")
        return
    
    # Step 3: Upload modified videos to Dailymotion
    print("\n=== STEP 3: UPLOADING VIDEOS TO DAILYMOTION ===")
    try:
        # Load the dailymotion_uploader module
        dm_uploader = load_module("dailymotion_uploader", DAILYMOTION_UPLOADER_PATH)
        
        # Find videos to upload
        uploaded_log = "uploaded_videos.log"
        videos_to_upload = dm_uploader.find_videos_to_upload(MODIFIED_DIR, uploaded_log)
        
        if not videos_to_upload:
            print("No new videos found to upload to Dailymotion.")
            return
        
        print(f"Found {len(videos_to_upload)} videos to upload to Dailymotion.")
        
        # Limit the number of videos to upload
        videos_to_upload = videos_to_upload[:UPLOAD_LIMIT]
        print(f"Will upload {len(videos_to_upload)} videos (limit: {UPLOAD_LIMIT})")
        
        # Create uploader
        uploader = dm_uploader.DailymotionUploader()
        
        # Upload videos
        for i, video_path in enumerate(videos_to_upload):
            print(f"\nUploading video {i+1}/{len(videos_to_upload)}: {video_path}")
            
            # Extract title from filename
            title = os.path.splitext(os.path.basename(video_path))[0]
            
            # Upload the video
            result = uploader.upload_video(
                video_path,
                title=title,
                tags=DAILYMOTION_TAGS,
                channel=DAILYMOTION_CHANNEL
            )
            
            if result.get('success'):
                # Log the uploaded video
                dm_uploader.log_uploaded_video(video_path, uploaded_log)
                print(f"Successfully uploaded: {title}")
                print(f"Video URL: {result['url']}")
            else:
                print(f"Failed to upload: {title}")
            
            # Add a small delay between uploads
            if i < len(videos_to_upload) - 1:
                time.sleep(5)
        
        print("\nDailymotion upload process completed.")
    except Exception as e:
        print(f"Error during Dailymotion upload process: {e}")
        return
    
    print("\n=== ALL PROCESSES COMPLETED SUCCESSFULLY ===")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Downloaded videos: {DOWNLOAD_DIR}")
    print(f"Modified videos: {MODIFIED_DIR}")
    print(f"Uploaded to Dailymotion: Check uploaded_videos.log for details")

if __name__ == "__main__":
    main()
