#!/usr/bin/env python3
"""
Simple web dashboard for monitoring the YouTube to Dailymotion pipeline
"""
import os
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from flask import Flask, render_template, jsonify, request
import psutil

from config import config
from logger import get_logger
from scheduler import scheduler
from pipeline_manager import pipeline_manager

logger = get_logger('dashboard')

app = Flask(__name__)

class DashboardData:
    """Data provider for the dashboard"""
    
    @staticmethod
    def get_system_stats():
        """Get current system statistics"""
        try:
            # CPU and Memory
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # Disk usage
            base_dir = config.get('base_dir')
            disk_usage = config.get_disk_usage(base_dir)
            
            # Process info
            try:
                process = psutil.Process()
                process_info = {
                    'pid': process.pid,
                    'memory_mb': round(process.memory_info().rss / 1024 / 1024, 2),
                    'cpu_percent': process.cpu_percent(),
                    'num_threads': process.num_threads(),
                    'status': process.status()
                }
            except:
                process_info = {'error': 'Process info unavailable'}
            
            return {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_gb': round(memory.available / 1024**3, 2),
                'disk_usage': disk_usage,
                'process_info': process_info,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Failed to get system stats: {e}")
            return {'error': str(e)}
    
    @staticmethod
    def get_pipeline_stats():
        """Get pipeline statistics"""
        try:
            stats = pipeline_manager.get_stats()
            
            # Add scheduler status
            scheduler_status = scheduler.get_status()
            stats['scheduler'] = scheduler_status
            
            return stats
        except Exception as e:
            logger.error(f"Failed to get pipeline stats: {e}")
            return {'error': str(e)}
    
    @staticmethod
    def get_recent_logs(lines=100):
        """Get recent log entries"""
        try:
            log_file = Path(config.get('log_dir')) / 'main.log'
            if not log_file.exists():
                return []
            
            with open(log_file, 'r') as f:
                log_lines = f.readlines()
            
            # Return last N lines
            recent_lines = log_lines[-lines:] if len(log_lines) > lines else log_lines
            
            return [line.strip() for line in recent_lines]
        except Exception as e:
            logger.error(f"Failed to get recent logs: {e}")
            return [f"Error reading logs: {e}"]
    
    @staticmethod
    def get_health_stats():
        """Get health statistics from file"""
        try:
            stats_file = Path(config.get('base_dir')) / 'health_stats.json'
            if not stats_file.exists():
                return {}
            
            with open(stats_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to get health stats: {e}")
            return {'error': str(e)}
    
    @staticmethod
    def get_file_counts():
        """Get file counts in different directories"""
        try:
            counts = {}
            
            # Downloads
            download_dir = Path(config.get('download_dir'))
            if download_dir.exists():
                counts['downloads'] = len([f for f in download_dir.rglob('*') if f.is_file()])
            else:
                counts['downloads'] = 0
            
            # Modified videos
            modified_dir = Path(config.get('modified_dir'))
            if modified_dir.exists():
                counts['modified'] = len([f for f in modified_dir.rglob('*') if f.is_file()])
            else:
                counts['modified'] = 0
            
            # Uploaded videos (from log)
            uploaded_log = Path(config.get('uploaded_log'))
            if uploaded_log.exists():
                with open(uploaded_log, 'r') as f:
                    counts['uploaded'] = len(f.readlines())
            else:
                counts['uploaded'] = 0
            
            return counts
        except Exception as e:
            logger.error(f"Failed to get file counts: {e}")
            return {'error': str(e)}

@app.route('/')
def dashboard():
    """Main dashboard page"""
    return render_template('dashboard.html')

@app.route('/api/status')
def api_status():
    """API endpoint for overall status"""
    try:
        data = {
            'system': DashboardData.get_system_stats(),
            'pipeline': DashboardData.get_pipeline_stats(),
            'health': DashboardData.get_health_stats(),
            'files': DashboardData.get_file_counts(),
            'timestamp': datetime.now().isoformat()
        }
        return jsonify(data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/logs')
def api_logs():
    """API endpoint for recent logs"""
    lines = request.args.get('lines', 100, type=int)
    logs = DashboardData.get_recent_logs(lines)
    return jsonify({'logs': logs})

@app.route('/api/config')
def api_config():
    """API endpoint for configuration (sanitized)"""
    try:
        # Return sanitized config (remove sensitive data)
        safe_config = config.config.copy()
        
        # Remove sensitive keys
        sensitive_keys = [
            'dailymotion_client_secret', 'dailymotion_password',
            'smtp_password', 'webhook_url'
        ]
        
        for key in sensitive_keys:
            if key in safe_config:
                safe_config[key] = '***HIDDEN***'
        
        return jsonify(safe_config)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Create templates directory and basic template
def create_dashboard_template():
    """Create basic dashboard template"""
    templates_dir = Path('templates')
    templates_dir.mkdir(exist_ok=True)
    
    template_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube to Dailymotion Pipeline Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stat-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; color: #2c3e50; }
        .stat-value { font-size: 24px; font-weight: bold; color: #27ae60; }
        .error { color: #e74c3c; }
        .warning { color: #f39c12; }
        .logs { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .log-entry { font-family: monospace; font-size: 12px; margin: 2px 0; padding: 2px; }
        .log-error { background-color: #ffebee; }
        .log-warning { background-color: #fff3e0; }
        .refresh-btn { background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
        .refresh-btn:hover { background: #2980b9; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>YouTube to Dailymotion Pipeline Dashboard</h1>
            <p>Real-time monitoring and status</p>
            <button class="refresh-btn" onclick="refreshData()">Refresh Data</button>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-title">System Status</div>
                <div id="system-status">Loading...</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-title">Pipeline Status</div>
                <div id="pipeline-status">Loading...</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-title">File Counts</div>
                <div id="file-counts">Loading...</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-title">Health Stats</div>
                <div id="health-stats">Loading...</div>
            </div>
        </div>
        
        <div class="logs">
            <div class="stat-title">Recent Logs</div>
            <div id="logs-container">Loading logs...</div>
        </div>
    </div>

    <script>
        function refreshData() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    updateSystemStatus(data.system);
                    updatePipelineStatus(data.pipeline);
                    updateFileCounts(data.files);
                    updateHealthStats(data.health);
                })
                .catch(error => console.error('Error:', error));
            
            fetch('/api/logs?lines=50')
                .then(response => response.json())
                .then(data => updateLogs(data.logs))
                .catch(error => console.error('Error:', error));
        }
        
        function updateSystemStatus(system) {
            const container = document.getElementById('system-status');
            if (system.error) {
                container.innerHTML = `<span class="error">Error: ${system.error}</span>`;
                return;
            }
            
            container.innerHTML = `
                <div>CPU: ${system.cpu_percent}%</div>
                <div>Memory: ${system.memory_percent}%</div>
                <div>Disk: ${system.disk_usage.usage_percent}%</div>
                <div>Free Space: ${system.disk_usage.free_gb} GB</div>
            `;
        }
        
        function updatePipelineStatus(pipeline) {
            const container = document.getElementById('pipeline-status');
            if (pipeline.error) {
                container.innerHTML = `<span class="error">Error: ${pipeline.error}</span>`;
                return;
            }
            
            const uptime = Math.round(pipeline.uptime_hours * 100) / 100;
            container.innerHTML = `
                <div>Uptime: ${uptime} hours</div>
                <div>Downloads: ${pipeline.stats.downloads.success}/${pipeline.stats.downloads.total}</div>
                <div>Modifications: ${pipeline.stats.modifications.success}/${pipeline.stats.modifications.total}</div>
                <div>Uploads: ${pipeline.stats.uploads.success}/${pipeline.stats.uploads.total}</div>
            `;
        }
        
        function updateFileCounts(files) {
            const container = document.getElementById('file-counts');
            if (files.error) {
                container.innerHTML = `<span class="error">Error: ${files.error}</span>`;
                return;
            }
            
            container.innerHTML = `
                <div>Downloaded: ${files.downloads}</div>
                <div>Modified: ${files.modified}</div>
                <div>Uploaded: ${files.uploaded}</div>
            `;
        }
        
        function updateHealthStats(health) {
            const container = document.getElementById('health-stats');
            if (health.error) {
                container.innerHTML = `<span class="error">Error: ${health.error}</span>`;
                return;
            }
            
            if (Object.keys(health).length === 0) {
                container.innerHTML = '<div>No health data available</div>';
                return;
            }
            
            container.innerHTML = `
                <div>Total Runs: ${health.total_runs || 0}</div>
                <div>Success Rate: ${health.total_runs ? Math.round((health.successful_runs / health.total_runs) * 100) : 0}%</div>
                <div>Consecutive Failures: ${health.consecutive_failures || 0}</div>
            `;
        }
        
        function updateLogs(logs) {
            const container = document.getElementById('logs-container');
            container.innerHTML = logs.map(log => {
                let className = 'log-entry';
                if (log.toLowerCase().includes('error')) className += ' log-error';
                else if (log.toLowerCase().includes('warning')) className += ' log-warning';
                
                return `<div class="${className}">${log}</div>`;
            }).join('');
        }
        
        // Auto-refresh every 30 seconds
        setInterval(refreshData, 30000);
        
        // Initial load
        refreshData();
    </script>
</body>
</html>'''
    
    with open(templates_dir / 'dashboard.html', 'w') as f:
        f.write(template_content)

def main():
    """Main function to run the dashboard"""
    # Create template if it doesn't exist
    create_dashboard_template()
    
    # Configure Flask
    app.config['DEBUG'] = config.get('debug', False)
    
    # Get host and port from config
    host = config.get('dashboard_host', '0.0.0.0')
    port = config.get('dashboard_port', 8080)
    
    logger.info(f"Starting web dashboard on {host}:{port}")
    
    try:
        app.run(host=host, port=port, debug=False)
    except Exception as e:
        logger.error(f"Failed to start web dashboard: {e}")

if __name__ == '__main__':
    main()
