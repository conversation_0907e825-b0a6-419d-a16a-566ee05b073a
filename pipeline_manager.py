"""
Enhanced pipeline manager for 24/7 YouTube to Dailymotion operation
"""
import os
import sys
import time
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
import importlib.util
import psutil

from config import config
from logger import get_logger
from scheduler import scheduler

logger = get_logger('main')

class PipelineManager:
    """Main pipeline manager with safety features and monitoring"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.stats = {
            'downloads': {'total': 0, 'success': 0, 'failed': 0},
            'modifications': {'total': 0, 'success': 0, 'failed': 0},
            'uploads': {'total': 0, 'success': 0, 'failed': 0}
        }
        
        # Load modules
        self.modules = {}
        self._load_modules()
        
    def _load_modules(self):
        """Load pipeline modules"""
        modules_to_load = {
            'youtube_downloader': 'youtube_downloader.py',
            'modif_video': 'modif_video.py',
            'dailymotion_uploader': 'dailymotion_uploader.py'
        }
        
        for module_name, file_path in modules_to_load.items():
            try:
                if os.path.exists(file_path):
                    spec = importlib.util.spec_from_file_location(module_name, file_path)
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)
                    self.modules[module_name] = module
                    logger.info(f"Loaded module: {module_name}")
                else:
                    logger.error(f"Module file not found: {file_path}")
            except Exception as e:
                logger.error(f"Failed to load module {module_name}: {e}")
    
    def check_prerequisites(self) -> bool:
        """Check if all prerequisites are met"""
        logger.info("Checking prerequisites...")
        
        # Check configuration
        if not config.validate():
            logger.error("Configuration validation failed")
            return False
        
        # Check disk space
        if not config.check_disk_space():
            logger.error("Insufficient disk space")
            return False
        
        # Check required modules
        required_modules = ['youtube_downloader', 'modif_video', 'dailymotion_uploader']
        for module_name in required_modules:
            if module_name not in self.modules:
                logger.error(f"Required module not loaded: {module_name}")
                return False
        
        # Check required files
        required_files = [
            config.get('channels_file'),
            config.get('keywords_file'),
            config.get('logo_path')
        ]
        
        for file_path in required_files:
            if file_path and not os.path.exists(file_path):
                logger.warning(f"Required file not found: {file_path}")
        
        # Create directories
        config.create_directories()
        
        logger.info("Prerequisites check completed")
        return True
    
    def cleanup_old_files(self):
        """Clean up old files to free disk space"""
        logger.info("Starting cleanup process...")
        
        try:
            # Clean up old downloads (keep last 7 days)
            download_dir = Path(config.get('download_dir'))
            if download_dir.exists():
                cutoff_date = datetime.now() - timedelta(days=7)
                self._cleanup_directory(download_dir, cutoff_date, "downloads")
            
            # Clean up old modified videos (keep last 3 days)
            modified_dir = Path(config.get('modified_dir'))
            if modified_dir.exists():
                cutoff_date = datetime.now() - timedelta(days=3)
                self._cleanup_directory(modified_dir, cutoff_date, "modified videos")
            
            # Clean up old logs (keep last 30 days)
            log_dir = Path(config.get('log_dir'))
            if log_dir.exists():
                cutoff_date = datetime.now() - timedelta(days=30)
                self._cleanup_directory(log_dir, cutoff_date, "logs", extensions=['.log'])
            
            logger.info("Cleanup process completed")
            
        except Exception as e:
            logger.error(f"Cleanup process failed: {e}")
    
    def _cleanup_directory(self, directory: Path, cutoff_date: datetime, 
                          description: str, extensions: List[str] = None):
        """Clean up files in directory older than cutoff date"""
        try:
            files_removed = 0
            space_freed = 0
            
            for file_path in directory.rglob('*'):
                if file_path.is_file():
                    # Check extension filter
                    if extensions and file_path.suffix.lower() not in extensions:
                        continue
                    
                    # Check file age
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_time < cutoff_date:
                        try:
                            file_size = file_path.stat().st_size
                            file_path.unlink()
                            files_removed += 1
                            space_freed += file_size
                            logger.debug(f"Removed old file: {file_path}")
                        except Exception as e:
                            logger.warning(f"Failed to remove file {file_path}: {e}")
            
            if files_removed > 0:
                space_freed_mb = space_freed / (1024 * 1024)
                logger.info(f"Cleaned up {files_removed} old {description} files, "
                           f"freed {space_freed_mb:.2f} MB")
            
        except Exception as e:
            logger.error(f"Failed to cleanup {description} directory: {e}")
    
    def download_videos(self) -> Dict[str, Any]:
        """Download videos from YouTube channels"""
        logger.info("=== STARTING VIDEO DOWNLOAD ===")
        start_time = time.time()
        
        try:
            # Check if downloader module is available
            if 'youtube_downloader' not in self.modules:
                raise Exception("YouTube downloader module not available")
            
            downloader = self.modules['youtube_downloader']
            
            # Create download directory
            download_dir = config.get('download_dir')
            os.makedirs(download_dir, exist_ok=True)
            
            # Check channels file
            channels_file = config.get('channels_file')
            if not os.path.exists(channels_file):
                logger.warning(f"Channels file not found: {channels_file}")
                return {'success': False, 'error': 'Channels file not found'}
            
            # Run download process
            result = downloader.check_channels_for_new_videos()
            
            duration = time.time() - start_time
            
            # Update stats
            self.stats['downloads']['total'] += 1
            if result:
                self.stats['downloads']['success'] += 1
                logger.log_operation("video_download", "success", duration=duration)
                return {'success': True, 'duration': duration}
            else:
                self.stats['downloads']['failed'] += 1
                logger.log_operation("video_download", "failed", duration=duration)
                return {'success': False, 'error': 'Download process failed'}
            
        except Exception as e:
            duration = time.time() - start_time
            self.stats['downloads']['failed'] += 1
            logger.error(f"Video download failed: {e}")
            logger.log_operation("video_download", "failed", duration=duration, error=str(e))
            return {'success': False, 'error': str(e)}
    
    def modify_videos(self) -> Dict[str, Any]:
        """Modify downloaded videos"""
        logger.info("=== STARTING VIDEO MODIFICATION ===")
        start_time = time.time()
        
        try:
            # Check if modifier module is available
            if 'modif_video' not in self.modules:
                raise Exception("Video modifier module not available")
            
            modifier = self.modules['modif_video']
            
            # Get directories
            download_dir = config.get('download_dir')
            modified_dir = config.get('modified_dir')
            
            # Create modified directory
            os.makedirs(modified_dir, exist_ok=True)
            
            # Find videos to process
            videos_to_process = []
            for root, dirs, files in os.walk(download_dir):
                for file in files:
                    if file.lower().endswith(('.mp4', '.mkv', '.avi', '.mov', '.flv', '.webm')):
                        video_path = os.path.join(root, file)
                        
                        # Check if already modified
                        rel_path = os.path.relpath(video_path, download_dir)
                        output_path = os.path.join(modified_dir, rel_path)
                        
                        if not os.path.exists(output_path):
                            videos_to_process.append((video_path, output_path))
            
            if not videos_to_process:
                logger.info("No new videos found to modify")
                return {'success': True, 'processed': 0}
            
            logger.info(f"Found {len(videos_to_process)} videos to modify")
            
            # Process videos
            processed_count = 0
            success_count = 0
            
            for video_path, output_path in videos_to_process:
                try:
                    # Check file size
                    file_size_gb = os.path.getsize(video_path) / (1024**3)
                    max_size = config.get('max_video_size_gb', 2.0)
                    
                    if file_size_gb > max_size:
                        logger.warning(f"Skipping large video ({file_size_gb:.2f}GB): {video_path}")
                        continue
                    
                    # Create parent directory
                    os.makedirs(os.path.dirname(output_path), exist_ok=True)
                    
                    # Modify video
                    video_start_time = time.time()
                    success = modifier.modify_video(
                        video_path,
                        output_path,
                        config.get('logo_path'),
                        config.get('channel_name'),
                        config.get('speed_factor'),
                        config.get('brightness_factor'),
                        config.get('contrast_factor'),
                        config.get('logo_position'),
                        config.get('logo_size')
                    )
                    
                    processing_time = time.time() - video_start_time
                    
                    if success:
                        success_count += 1
                        logger.log_video_processing(
                            video_path, "modification", "success",
                            file_size_mb=file_size_gb * 1024,
                            processing_time=processing_time
                        )
                    else:
                        logger.log_video_processing(
                            video_path, "modification", "failed",
                            processing_time=processing_time
                        )
                    
                    processed_count += 1
                    
                    # Add delay between processing
                    time.sleep(config.get('rate_limit_delay', 60))
                    
                except Exception as e:
                    logger.error(f"Failed to modify video {video_path}: {e}")
                    logger.log_video_processing(video_path, "modification", "error", error=str(e))
            
            duration = time.time() - start_time
            
            # Update stats
            self.stats['modifications']['total'] += 1
            if success_count > 0:
                self.stats['modifications']['success'] += 1
                logger.log_operation(
                    "video_modification", "success",
                    duration=duration,
                    files_processed=processed_count,
                    success_count=success_count
                )
            else:
                self.stats['modifications']['failed'] += 1
                logger.log_operation(
                    "video_modification", "failed",
                    duration=duration,
                    files_processed=processed_count
                )
            
            return {
                'success': success_count > 0,
                'processed': processed_count,
                'success_count': success_count,
                'duration': duration
            }
            
        except Exception as e:
            duration = time.time() - start_time
            self.stats['modifications']['failed'] += 1
            logger.error(f"Video modification failed: {e}")
            logger.log_operation("video_modification", "failed", duration=duration, error=str(e))
            return {'success': False, 'error': str(e)}
    
    def upload_videos(self) -> Dict[str, Any]:
        """Upload modified videos to Dailymotion"""
        logger.info("=== STARTING VIDEO UPLOAD ===")
        start_time = time.time()
        
        try:
            # Check if uploader module is available
            if 'dailymotion_uploader' not in self.modules:
                raise Exception("Dailymotion uploader module not available")
            
            uploader_module = self.modules['dailymotion_uploader']
            
            # Get directories and files
            modified_dir = config.get('modified_dir')
            uploaded_log = config.get('uploaded_log')
            
            # Find videos to upload
            videos_to_upload = uploader_module.find_videos_to_upload(modified_dir, uploaded_log)
            
            if not videos_to_upload:
                logger.info("No new videos found to upload")
                return {'success': True, 'uploaded': 0}
            
            # Limit uploads per run
            upload_limit = config.get('upload_limit', 5)
            videos_to_upload = videos_to_upload[:upload_limit]
            
            logger.info(f"Found {len(videos_to_upload)} videos to upload")
            
            # Create uploader
            uploader = uploader_module.DailymotionUploader()
            
            # Upload videos
            uploaded_count = 0
            success_count = 0
            
            for video_path in videos_to_upload:
                try:
                    # Extract title from filename
                    title = os.path.splitext(os.path.basename(video_path))[0]
                    
                    # Upload video
                    upload_start_time = time.time()
                    result = uploader.upload_video(
                        video_path,
                        title=title,
                        tags=config.get('dailymotion_tags'),
                        channel=config.get('dailymotion_channel')
                    )
                    
                    upload_time = time.time() - upload_start_time
                    
                    if result.get('success'):
                        # Log successful upload
                        uploader_module.log_uploaded_video(video_path, uploaded_log)
                        success_count += 1
                        
                        logger.log_video_processing(
                            video_path, "upload", "success",
                            processing_time=upload_time,
                            video_url=result.get('url')
                        )
                    else:
                        logger.log_video_processing(
                            video_path, "upload", "failed",
                            processing_time=upload_time,
                            error=result.get('error')
                        )
                    
                    uploaded_count += 1
                    
                    # Add delay between uploads
                    time.sleep(config.get('rate_limit_delay', 60))
                    
                except Exception as e:
                    logger.error(f"Failed to upload video {video_path}: {e}")
                    logger.log_video_processing(video_path, "upload", "error", error=str(e))
            
            duration = time.time() - start_time
            
            # Update stats
            self.stats['uploads']['total'] += 1
            if success_count > 0:
                self.stats['uploads']['success'] += 1
                logger.log_operation(
                    "video_upload", "success",
                    duration=duration,
                    files_processed=uploaded_count,
                    success_count=success_count
                )
            else:
                self.stats['uploads']['failed'] += 1
                logger.log_operation(
                    "video_upload", "failed",
                    duration=duration,
                    files_processed=uploaded_count
                )
            
            return {
                'success': success_count > 0,
                'uploaded': uploaded_count,
                'success_count': success_count,
                'duration': duration
            }
            
        except Exception as e:
            duration = time.time() - start_time
            self.stats['uploads']['failed'] += 1
            logger.error(f"Video upload failed: {e}")
            logger.log_operation("video_upload", "failed", duration=duration, error=str(e))
            return {'success': False, 'error': str(e)}
    
    def run_full_pipeline(self) -> Dict[str, Any]:
        """Run the complete pipeline"""
        logger.info("=== STARTING FULL PIPELINE ===")
        pipeline_start_time = time.time()
        
        results = {
            'start_time': datetime.now().isoformat(),
            'download': None,
            'modify': None,
            'upload': None,
            'cleanup': None,
            'success': False,
            'total_duration': 0
        }
        
        try:
            # Check prerequisites
            if not self.check_prerequisites():
                results['error'] = 'Prerequisites check failed'
                return results
            
            # Step 1: Download videos
            results['download'] = self.download_videos()
            
            # Step 2: Modify videos
            results['modify'] = self.modify_videos()
            
            # Step 3: Upload videos
            results['upload'] = self.upload_videos()
            
            # Step 4: Cleanup old files
            self.cleanup_old_files()
            results['cleanup'] = {'success': True}
            
            # Calculate total duration
            results['total_duration'] = time.time() - pipeline_start_time
            
            # Determine overall success
            results['success'] = (
                results['download'].get('success', False) or
                results['modify'].get('success', False) or
                results['upload'].get('success', False)
            )
            
            logger.info(f"=== PIPELINE COMPLETED ===")
            logger.info(f"Total duration: {results['total_duration']:.2f} seconds")
            logger.info(f"Overall success: {results['success']}")
            
            return results
            
        except Exception as e:
            results['total_duration'] = time.time() - pipeline_start_time
            results['error'] = str(e)
            logger.error(f"Pipeline failed: {e}")
            logger.exception("Pipeline exception details")
            return results
    
    def get_stats(self) -> Dict[str, Any]:
        """Get pipeline statistics"""
        uptime = (datetime.now() - self.start_time).total_seconds()
        
        return {
            'start_time': self.start_time.isoformat(),
            'uptime_hours': uptime / 3600,
            'stats': self.stats,
            'modules_loaded': list(self.modules.keys())
        }

# Global pipeline manager instance
pipeline_manager = PipelineManager()
