#!/usr/bin/env python3
"""
Management script for isolated YouTube to Dailymotion pipeline
Safe for multi-project VPS environments
"""
import os
import sys
import argparse
import subprocess
import json
import time
from pathlib import Path
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from isolated_config import config
    from logger import get_logger
except ImportError:
    print("Error: Could not import pipeline modules. Make sure you're in the correct directory.")
    sys.exit(1)

logger = get_logger('manager')

class PipelineManager:
    """Management interface for the isolated pipeline"""
    
    def __init__(self):
        self.service_name = "yt-dm-pipeline"
        self.install_dir = config.get('base_dir', '/opt/youtube-dailymotion-pipeline')
        
    def check_service_status(self) -> dict:
        """Check if the systemd service is running"""
        try:
            result = subprocess.run(
                ['systemctl', 'is-active', self.service_name],
                capture_output=True, text=True
            )
            
            active = result.returncode == 0
            status = result.stdout.strip() if result.stdout else 'unknown'
            
            return {
                'active': active,
                'status': status,
                'service_name': self.service_name
            }
        except Exception as e:
            return {
                'active': False,
                'status': 'error',
                'error': str(e)
            }
    
    def check_process_status(self) -> dict:
        """Check if the pipeline process is running"""
        try:
            pid_file = Path(self.install_dir) / 'daemon.pid'
            
            if not pid_file.exists():
                return {'running': False, 'reason': 'No PID file'}
            
            with open(pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            # Check if process exists
            try:
                os.kill(pid, 0)
                return {'running': True, 'pid': pid}
            except OSError:
                return {'running': False, 'reason': f'Process {pid} not found'}
                
        except Exception as e:
            return {'running': False, 'reason': str(e)}
    
    def get_resource_usage(self) -> dict:
        """Get current resource usage"""
        try:
            import psutil
            
            # System resources
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # Disk usage for our directory
            disk_usage = config.get_disk_usage(self.install_dir)
            
            # Check if resources are available for pipeline
            resource_checks = config.check_system_resources()
            
            return {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_gb': round(memory.available / 1024**3, 2),
                'disk_usage': disk_usage,
                'resource_checks': resource_checks,
                'can_run': all(resource_checks.values())
            }
        except Exception as e:
            return {'error': str(e)}
    
    def get_pipeline_stats(self) -> dict:
        """Get pipeline statistics"""
        try:
            stats_file = Path(self.install_dir) / 'health_stats.json'
            
            if not stats_file.exists():
                return {'error': 'No statistics available'}
            
            with open(stats_file, 'r') as f:
                stats = json.load(f)
            
            return stats
        except Exception as e:
            return {'error': str(e)}
    
    def get_recent_logs(self, lines: int = 20) -> list:
        """Get recent log entries"""
        try:
            log_file = Path(self.install_dir) / 'logs' / 'main.log'
            
            if not log_file.exists():
                return ['No log file found']
            
            with open(log_file, 'r') as f:
                log_lines = f.readlines()
            
            return [line.strip() for line in log_lines[-lines:]]
        except Exception as e:
            return [f'Error reading logs: {e}']
    
    def start_service(self) -> bool:
        """Start the pipeline service"""
        try:
            # Check if already running
            service_status = self.check_service_status()
            if service_status['active']:
                print(f"Service {self.service_name} is already running")
                return True
            
            # Check resources before starting
            resources = self.get_resource_usage()
            if not resources.get('can_run', False):
                print("Warning: System resources are currently high.")
                print("The pipeline will automatically pause if needed.")
                print("Resource status:", resources.get('resource_checks', {}))
            
            # Start the service
            result = subprocess.run(
                ['sudo', 'systemctl', 'start', self.service_name],
                capture_output=True, text=True
            )
            
            if result.returncode == 0:
                print(f"Service {self.service_name} started successfully")
                time.sleep(2)  # Wait a moment
                
                # Verify it's running
                status = self.check_service_status()
                if status['active']:
                    print("✅ Service is now active")
                    return True
                else:
                    print(f"❌ Service failed to start: {status}")
                    return False
            else:
                print(f"Failed to start service: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"Error starting service: {e}")
            return False
    
    def stop_service(self) -> bool:
        """Stop the pipeline service"""
        try:
            result = subprocess.run(
                ['sudo', 'systemctl', 'stop', self.service_name],
                capture_output=True, text=True
            )
            
            if result.returncode == 0:
                print(f"Service {self.service_name} stopped successfully")
                return True
            else:
                print(f"Failed to stop service: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"Error stopping service: {e}")
            return False
    
    def restart_service(self) -> bool:
        """Restart the pipeline service"""
        print("Restarting pipeline service...")
        if self.stop_service():
            time.sleep(2)
            return self.start_service()
        return False
    
    def pause_pipeline(self) -> bool:
        """Pause the pipeline (send SIGUSR1)"""
        try:
            process_status = self.check_process_status()
            if not process_status['running']:
                print("Pipeline is not running")
                return False
            
            pid = process_status['pid']
            os.kill(pid, 10)  # SIGUSR1
            print("Pause signal sent to pipeline")
            return True
            
        except Exception as e:
            print(f"Error pausing pipeline: {e}")
            return False
    
    def resume_pipeline(self) -> bool:
        """Resume the pipeline (send SIGUSR2)"""
        try:
            process_status = self.check_process_status()
            if not process_status['running']:
                print("Pipeline is not running")
                return False
            
            pid = process_status['pid']
            os.kill(pid, 12)  # SIGUSR2
            print("Resume signal sent to pipeline")
            return True
            
        except Exception as e:
            print(f"Error resuming pipeline: {e}")
            return False
    
    def show_status(self):
        """Show comprehensive status"""
        print("=" * 60)
        print("YouTube to Dailymotion Pipeline Status")
        print("=" * 60)
        
        # Service status
        service_status = self.check_service_status()
        print(f"Service Status: {service_status['status']}")
        
        # Process status
        process_status = self.check_process_status()
        if process_status['running']:
            print(f"Process Status: Running (PID: {process_status['pid']})")
        else:
            print(f"Process Status: Not running ({process_status['reason']})")
        
        # Resource usage
        print("\nResource Usage:")
        resources = self.get_resource_usage()
        if 'error' not in resources:
            print(f"  CPU: {resources['cpu_percent']:.1f}%")
            print(f"  Memory: {resources['memory_percent']:.1f}%")
            print(f"  Available Memory: {resources['memory_available_gb']:.1f} GB")
            print(f"  Disk Usage: {resources['disk_usage']['usage_percent']:.1f}%")
            print(f"  Free Space: {resources['disk_usage']['free_gb']:.1f} GB")
            
            # Resource checks
            checks = resources['resource_checks']
            print(f"  Resource Status: {'✅ Good' if resources['can_run'] else '⚠️  High Load'}")
            for check, status in checks.items():
                status_icon = "✅" if status else "❌"
                print(f"    {check}: {status_icon}")
        else:
            print(f"  Error: {resources['error']}")
        
        # Pipeline statistics
        print("\nPipeline Statistics:")
        stats = self.get_pipeline_stats()
        if 'error' not in stats:
            print(f"  Total Runs: {stats.get('total_runs', 0)}")
            print(f"  Successful Runs: {stats.get('successful_runs', 0)}")
            print(f"  Failed Runs: {stats.get('failed_runs', 0)}")
            print(f"  Paused Runs: {stats.get('paused_runs', 0)}")
            print(f"  Resource Pauses: {stats.get('resource_pauses', 0)}")
            
            if 'start_time' in stats:
                start_time = datetime.fromisoformat(stats['start_time'])
                uptime = datetime.now() - start_time
                print(f"  Uptime: {uptime}")
        else:
            print(f"  Error: {stats['error']}")
        
        # Configuration
        print(f"\nConfiguration:")
        print(f"  Install Directory: {self.install_dir}")
        print(f"  Dashboard Port: {config.get('dashboard_port', 'Not configured')}")
        print(f"  Log Directory: {config.get('log_dir')}")
        
        print("=" * 60)
    
    def show_logs(self, lines: int = 20):
        """Show recent logs"""
        print(f"Recent logs ({lines} lines):")
        print("-" * 60)
        
        logs = self.get_recent_logs(lines)
        for log_line in logs:
            print(log_line)
        
        print("-" * 60)
    
    def test_configuration(self) -> bool:
        """Test pipeline configuration"""
        print("Testing pipeline configuration...")
        
        try:
            # Test configuration validation
            if not config.validate_isolation():
                print("❌ Configuration validation failed")
                return False
            print("✅ Configuration is valid")
            
            # Test directory access
            config.create_isolated_directories()
            print("✅ Directories are accessible")
            
            # Test resource availability
            resources = self.get_resource_usage()
            if resources.get('can_run', False):
                print("✅ Resources are available")
            else:
                print("⚠️  Resources are currently limited")
                print("   Pipeline will pause automatically when needed")
            
            # Test required files
            required_files = [
                config.get('channels_file'),
                config.get('keywords_file')
            ]
            
            missing_files = []
            for file_path in required_files:
                if not os.path.exists(file_path):
                    missing_files.append(file_path)
            
            if missing_files:
                print("⚠️  Missing configuration files:")
                for file_path in missing_files:
                    print(f"   - {file_path}")
                print("   Create these files before starting the pipeline")
            else:
                print("✅ Configuration files exist")
            
            print("\n✅ Configuration test completed")
            return True
            
        except Exception as e:
            print(f"❌ Configuration test failed: {e}")
            return False

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description='Manage YouTube to Dailymotion Pipeline (Isolated)',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python manage.py status          # Show status
  python manage.py start           # Start pipeline
  python manage.py stop            # Stop pipeline
  python manage.py pause           # Pause operations
  python manage.py resume          # Resume operations
  python manage.py logs            # Show recent logs
  python manage.py test            # Test configuration
        """
    )
    
    parser.add_argument('command', 
                       choices=['start', 'stop', 'restart', 'status', 'logs', 'test', 'pause', 'resume'],
                       help='Management command')
    parser.add_argument('--lines', '-n', type=int, default=20,
                       help='Number of log lines to show (default: 20)')
    
    args = parser.parse_args()
    
    manager = PipelineManager()
    
    if args.command == 'start':
        success = manager.start_service()
        sys.exit(0 if success else 1)
        
    elif args.command == 'stop':
        success = manager.stop_service()
        sys.exit(0 if success else 1)
        
    elif args.command == 'restart':
        success = manager.restart_service()
        sys.exit(0 if success else 1)
        
    elif args.command == 'status':
        manager.show_status()
        
    elif args.command == 'logs':
        manager.show_logs(args.lines)
        
    elif args.command == 'test':
        success = manager.test_configuration()
        sys.exit(0 if success else 1)
        
    elif args.command == 'pause':
        success = manager.pause_pipeline()
        sys.exit(0 if success else 1)
        
    elif args.command == 'resume':
        success = manager.resume_pipeline()
        sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
