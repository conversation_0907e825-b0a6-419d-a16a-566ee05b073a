import os
import sys
import json
import time
import requests
import argparse
from datetime import datetime
import logging
from pathlib import Path
import concurrent.futures
import hashlib
import re

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("dailymotion_uploader.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Dailymotion API endpoints
API_BASE_URL = "https://api.dailymotion.com"
AUTH_URL = f"{API_BASE_URL}/oauth/token"
UPLOAD_URL = f"{API_BASE_URL}/file/upload"
VIDEO_URL = f"{API_BASE_URL}/videos"

# Configuration
TOKEN_FILE = "token.json"
DEFAULT_VIDEO_DIR = "/home/<USER>/modified_videos"  # Directory containing modified videos
UPLOAD_LIMIT_PER_RUN = 5  # Maximum number of videos to upload in one run
CONCURRENT_UPLOADS = 1  # Number of concurrent uploads (be careful with API limits)
RETRY_ATTEMPTS = 3  # Number of retry attempts for failed uploads
RETRY_DELAY = 30  # Delay in seconds between retry attempts

# Video metadata defaults
DEFAULT_CHANNEL = "sport"  # Default Dailymotion channel
DEFAULT_TAGS = ["sports", "highlights", "fastinsport"]  # Default tags
DEFAULT_PUBLISHED = True  # Whether to publish immediately
DEFAULT_PRIVATE = False  # Whether to make the video private
DEFAULT_DESCRIPTION_TEMPLATE = "Uploaded by Fast in Sport on {date}. {custom_text}"

class DailymotionUploader:
    def __init__(self, token_file=TOKEN_FILE):
        self.token_file = token_file
        self.auth_data = self._load_auth_data()
        self.session = requests.Session()
        self.upload_stats = {"success": 0, "failed": 0, "skipped": 0}
        
    def _load_auth_data(self):
        """Load authentication data from token file."""
        try:
            with open(self.token_file, 'r') as f:
                auth_data = json.load(f)
                logger.info("Successfully loaded authentication data from token file")
                return auth_data
        except FileNotFoundError:
            logger.error(f"Token file not found: {self.token_file}")
            raise
        except json.JSONDecodeError:
            logger.error(f"Invalid JSON in token file: {self.token_file}")
            raise
            
    def _refresh_token_if_needed(self):
        """Check if token is expired and refresh if needed."""
        # Refresh if token is expired or will expire in the next 5 minutes
        if 'expires_at' in self.auth_data and time.time() >= (self.auth_data['expires_at'] - 300):
            logger.info("Access token expired or will expire soon, refreshing...")
            self._refresh_token()
        
    def _refresh_token(self):
        """Refresh the access token using the refresh token."""
        if 'refresh_token' not in self.auth_data:
            logger.error("No refresh token available")
            raise ValueError("No refresh token available in token file")
            
        data = {
            'grant_type': 'refresh_token',
            'client_id': self.auth_data['client_id'],
            'client_secret': self.auth_data['client_secret'],
            'refresh_token': self.auth_data['refresh_token']
        }
        
        try:
            response = self.session.post(AUTH_URL, data=data)
            response.raise_for_status()
            token_data = response.json()
            
            # Update auth data
            self.auth_data['access_token'] = token_data['access_token']
            if 'refresh_token' in token_data:
                self.auth_data['refresh_token'] = token_data['refresh_token']
            self.auth_data['expires_at'] = time.time() + token_data['expires_in']
            
            # Save updated token
            self._save_auth_data()
            logger.info("Successfully refreshed access token")
        except requests.RequestException as e:
            logger.error(f"Failed to refresh token: {e}")
            raise
            
    def _save_auth_data(self):
        """Save authentication data back to token file."""
        try:
            with open(self.token_file, 'w') as f:
                json.dump(self.auth_data, f, indent=2)
            logger.info("Saved updated authentication data to token file")
        except Exception as e:
            logger.error(f"Failed to save authentication data: {e}")
    
    def get_video_info(self, video_id):
        """Get information about a video from Dailymotion."""
        self._refresh_token_if_needed()
        headers = {"Authorization": f"Bearer {self.auth_data['access_token']}"}
        
        try:
            response = self.session.get(f"{VIDEO_URL}/{video_id}", headers=headers)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.error(f"Failed to get video info for {video_id}: {e}")
            return None
    
    def update_video(self, video_id, **metadata):
        """Update metadata for an existing video."""
        self._refresh_token_if_needed()
        headers = {"Authorization": f"Bearer {self.auth_data['access_token']}"}
        
        try:
            response = self.session.post(f"{VIDEO_URL}/{video_id}", data=metadata, headers=headers)
            response.raise_for_status()
            logger.info(f"Updated metadata for video {video_id}")
            return True
        except requests.RequestException as e:
            logger.error(f"Failed to update video {video_id}: {e}")
            return False
    
    def delete_video(self, video_id):
        """Delete a video from Dailymotion."""
        self._refresh_token_if_needed()
        headers = {"Authorization": f"Bearer {self.auth_data['access_token']}"}
        
        try:
            response = self.session.delete(f"{VIDEO_URL}/{video_id}", headers=headers)
            response.raise_for_status()
            logger.info(f"Deleted video {video_id}")
            return True
        except requests.RequestException as e:
            logger.error(f"Failed to delete video {video_id}: {e}")
            return False
    
    def get_channel_videos(self, limit=50):
        """Get list of videos in your channel."""
        self._refresh_token_if_needed()
        headers = {"Authorization": f"Bearer {self.auth_data['access_token']}"}
        
        try:
            response = self.session.get(
                f"{API_BASE_URL}/me/videos",
                params={"limit": limit, "fields": "id,title,url,status"},
                headers=headers
            )
            response.raise_for_status()
            return response.json().get('list', [])
        except requests.RequestException as e:
            logger.error(f"Failed to get channel videos: {e}")
            return []
            
    def upload_video(self, video_path, title=None, description=None, tags=None, 
                     channel=DEFAULT_CHANNEL, published=DEFAULT_PUBLISHED, private=DEFAULT_PRIVATE,
                     custom_description="", retry_count=0):
        """Upload a video to Dailymotion."""
        # Ensure we have a valid token
        self._refresh_token_if_needed()
        
        # Get file info
        video_file = Path(video_path)
        if not video_file.exists():
            logger.error(f"Video file not found: {video_path}")
            return {'success': False, 'error': 'File not found'}
            
        # Set default metadata if not provided
        if title is None:
            title = self._clean_title(video_file.stem)
        if description is None:
            description = DEFAULT_DESCRIPTION_TEMPLATE.format(
                date=datetime.now().strftime('%Y-%m-%d'),
                custom_text=custom_description
            )
        if tags is None:
            tags = DEFAULT_TAGS
            
        logger.info(f"Starting upload for: {video_path}")
        
        try:
            # Step 1: Get upload URL
            headers = {"Authorization": f"Bearer {self.auth_data['access_token']}"}
            response = self.session.get(UPLOAD_URL, headers=headers)
            response.raise_for_status()
            upload_data = response.json()
            
            # Step 2: Upload the file to the provided URL
            file_size = video_file.stat().st_size
            logger.info(f"Got upload URL, uploading file ({self._format_size(file_size)})...")
            
            with open(video_path, 'rb') as f:
                upload_response = requests.post(
                    upload_data['upload_url'],
                    files={'file': (video_file.name, f, 'video/mp4')}
                )
                upload_response.raise_for_status()
                
            # Step 3: Create the video with metadata
            logger.info("File uploaded, creating video with metadata...")
            video_data = {
                'url': upload_data['url'],
                'title': title,
                'description': description,
                'tags': ','.join(tags),
                'channel': channel,
                'published': str(published).lower(),
                'private': str(private).lower()
            }
            
            create_response = self.session.post(
                VIDEO_URL,
                data=video_data,
                headers=headers
            )
            create_response.raise_for_status()
            video_info = create_response.json()
            
            logger.info(f"Successfully uploaded video: {title}")
            logger.info(f"Video ID: {video_info['id']}")
            logger.info(f"Video URL: https://www.dailymotion.com/video/{video_info['id']}")
            
            self.upload_stats["success"] += 1
            
            return {
                'success': True,
                'video_id': video_info['id'],
                'url': f"https://www.dailymotion.com/video/{video_info['id']}",
                'title': title
            }
            
        except requests.RequestException as e:
            logger.error(f"Upload failed: {e}")
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"Response: {e.response.text}")
            
            # Retry logic
            if retry_count < RETRY_ATTEMPTS:
                retry_count += 1
                logger.info(f"Retrying upload ({retry_count}/{RETRY_ATTEMPTS}) in {RETRY_DELAY} seconds...")
                time.sleep(RETRY_DELAY)
                return self.upload_video(video_path, title, description, tags, 
                                        channel, published, private, 
                                        custom_description, retry_count)
            else:
                self.upload_stats["failed"] += 1
                return {'success': False, 'error': str(e)}
    
    def _clean_title(self, title):
        """Clean and format the video title."""
        # Remove special characters and limit length
        title = re.sub(r'[^\w\s\-\.]', '', title)
        if len(title) > 100:
            title = title[:97] + "..."
        return title
    
    def _format_size(self, size_bytes):
        """Format file size in human-readable format."""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.2f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.2f} TB"
    
    def get_upload_stats(self):
        """Get statistics about the upload session."""
        return self.upload_stats

def find_videos_to_upload(directory, uploaded_log=None, min_size_mb=1, max_size_gb=2):
    """Find video files in the directory that haven't been uploaded yet."""
    # Load previously uploaded videos if log exists
    uploaded_videos = set()
    if uploaded_log and os.path.exists(uploaded_log):
        try:
            with open(uploaded_log, 'r') as f:
                for line in f:
                    if line.strip():
                        uploaded_videos.add(line.strip())
        except Exception as e:
            logger.error(f"Error reading upload log: {e}")
    
    # Find video files
    video_files = []
    min_size_bytes = min_size_mb * 1024 * 1024
    max_size_bytes = max_size_gb * 1024 * 1024 * 1024
    
    for root, _, files in os.walk(directory):
        for file in files:
            if file.lower().endswith(('.mp4', '.mkv', '.avi', '.mov', '.flv', '.webm')):
                full_path = os.path.join(root, file)
                
                # Skip if already uploaded
                if full_path in uploaded_videos:
                    logger.debug(f"Skipping already uploaded video: {full_path}")
                    continue
                
                # Check file size
                file_size = os.path.getsize(full_path)
                if file_size < min_size_bytes:
                    logger.warning(f"Skipping video that's too small ({file_size/1024/1024:.2f} MB): {full_path}")
                    continue
                if file_size > max_size_bytes:
                    logger.warning(f"Skipping video that's too large ({file_size/1024/1024/1024:.2f} GB): {full_path}")
                    continue
                
                video_files.append(full_path)
    
    # Sort by modification time (newest first)
    video_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    return video_files

def log_uploaded_video(video_path, uploaded_log):
    """Log the uploaded video to prevent re-uploading."""
    try:
        with open(uploaded_log, 'a') as f:
            f.write(f"{video_path}\n")
    except Exception as e:
        logger.error(f"Error logging uploaded video: {e}")

def calculate_file_hash(file_path, block_size=65536):
    """Calculate SHA-256 hash of a file to identify it uniquely."""
    file_hash = hashlib.sha256()
    with open(file_path, 'rb') as f:
        for block in iter(lambda: f.read(block_size), b''):
            file_hash.update(block)
    return file_hash.hexdigest()

def upload_worker(args):
    """Worker function for parallel uploads."""
    uploader, video_path, index, total, params = args
    logger.info(f"Uploading video {index+1}/{total}: {video_path}")
    
    # Extract title from filename
    title = os.path.splitext(os.path.basename(video_path))[0]
    
    # Calculate file hash for unique identification
    file_hash = calculate_file_hash(video_path)
    
    # Upload the video
    result = uploader.upload_video(
        video_path,
        title=title,
        tags=params['tags'],
        channel=params['channel'],
        published=params['published'],
        private=params['private'],
        custom_description=params.get('custom_description', '')
    )
    
    return video_path, result, file_hash

def batch_upload_videos(uploader, videos, uploaded_log, **params):
    """Upload multiple videos in parallel."""
    if not videos:
        logger.info("No videos to upload")
        return
    
    total = len(videos)
    logger.info(f"Preparing to upload {total} videos")
    
    if params.get('concurrent', CONCURRENT_UPLOADS) > 1:
        # Parallel uploads
        with concurrent.futures.ThreadPoolExecutor(max_workers=params.get('concurrent', CONCURRENT_UPLOADS)) as executor:
            args = [(uploader, video, i, total, params) for i, video in enumerate(videos)]
            for video_path, result, file_hash in executor.map(upload_worker, args):
                if result.get('success'):
                    # Log the uploaded video
                    log_uploaded_video(video_path, uploaded_log)
                    logger.info(f"Successfully uploaded: {result.get('title', os.path.basename(video_path))}")
                    logger.info(f"Video URL: {result['url']}")
                else:
                    logger.error(f"Failed to upload: {os.path.basename(video_path)}")
    else:
        # Sequential uploads
        for i, video_path in enumerate(videos):
            video_path, result, file_hash = upload_worker((uploader, video_path, i, total, params))
            
            if result.get('success'):
                # Log the uploaded video
                log_uploaded_video(video_path, uploaded_log)
                logger.info(f"Successfully uploaded: {result.get('title', os.path.basename(video_path))}")
                logger.info(f"Video URL: {result['url']}")
            else:
                logger.error(f"Failed to upload: {os.path.basename(video_path)}")
            
            # Add a small delay between uploads
            if i < len(videos) - 1:
                time.sleep(5)

def main():
    parser = argparse.ArgumentParser(description="Upload videos to Dailymotion")
    parser.add_argument("--directory", "-d", default=DEFAULT_VIDEO_DIR,
                        help=f"Directory containing videos to upload (default: {DEFAULT_VIDEO_DIR})")
    parser.add_argument("--token", "-t", default=TOKEN_FILE,
                        help=f"Path to token.json file (default: {TOKEN_FILE})")
    parser.add_argument("--limit", "-l", type=int, default=UPLOAD_LIMIT_PER_RUN,
                        help=f"Maximum number of videos to upload (default: {UPLOAD_LIMIT_PER_RUN})")
    parser.add_argument("--channel", "-c", default=DEFAULT_CHANNEL,
                        help=f"Dailymotion channel (default: {DEFAULT_CHANNEL})")
    parser.add_argument("--tags", default=','.join(DEFAULT_TAGS),
                        help=f"Video tags, comma-separated (default: {','.join(DEFAULT_TAGS)})")
    parser.add_argument("--private", action="store_true",
                        help="Make videos private (default: public)")
    parser.add_argument("--unpublished", action="store_true",
                        help="Don't publish videos immediately (default: publish)")
    parser.add_argument("--concurrent", "-j", type=int, default=CONCURRENT_UPLOADS,
                        help=f"Number of concurrent uploads (default: {CONCURRENT_UPLOADS})")
    parser.add_argument("--description", type=str, default="",
                        help="Custom text to add to video descriptions")
    parser.add_argument("--min-size", type=float, default=1,
                        help="Minimum video size in MB (default: 1)")
    parser.add_argument("--max-size", type=float, default=2048,
                        help="Maximum video size in MB (default: 2048)")
    parser.add_argument("--list-videos", action="store_true",
                        help="List videos in your Dailymotion channel and exit")
    parser.add_argument("--delete-video", type=str, default=None,
                        help="Delete a video by ID and exit")
    
    args = parser.parse_args()
    
    # Convert tags string to list
    tags = [tag.strip() for tag in args.tags.split(',') if tag.strip()]
    
    # Create uploader
    try:
        uploader = DailymotionUploader(token_file=args.token)
    except Exception as e:
        logger.error(f"Failed to initialize uploader: {e}")
        return
    
    # Handle special commands
    if args.list_videos:
        videos = uploader.get_channel_videos()
        print(f"\nYour Dailymotion Videos ({len(videos)}):")
        for video in videos:
            print(f"ID: {video['id']} | Title: {video['title']} | Status: {video['status']}")
            print(f"URL: https://www.dailymotion.com/video/{video['id']}")
        return
    
    if args.delete_video:
        if uploader.delete_video(args.delete_video):
            print(f"Video {args.delete_video} deleted successfully.")
        else:
            print(f"Failed to delete video {args.delete_video}.")
        return
    
    # Find videos to upload
    uploaded_log = "uploaded_videos.log"
    videos = find_videos_to_upload(args.directory, uploaded_log, args.min_size, args.max_size)
    
    if not videos:
        logger.info("No new videos found to upload")
        return
    
    logger.info(f"Found {len(videos)} videos to upload")
    
    # Limit the number of videos to upload
    videos = videos[:args.limit]
    logger.info(f"Will upload {len(videos)} videos (limit: {args.limit})")
    
    # Upload videos
    batch_upload_videos(uploader, videos, uploaded_log, 
                        tags=tags, 
                        channel=args.channel, 
                        published=not args.unpublished, 
                        private=args.private, 
                        custom_description=args.description, 
                        concurrent=args.concurrent)
    
    # Print upload statistics
    stats = uploader.get_upload_stats()
    logger.info(f"Upload session statistics: {stats}")

# Additional utility functions for advanced features

def check_video_status(uploader, video_id):
    """Check the processing status of a video."""
    video_info = uploader.get_video_info(video_id)
    if not video_info:
        return "Unknown"
    return video_info.get('status', 'Unknown')

def update_video_metadata(uploader, video_id, title=None, description=None, tags=None, channel=None):
    """Update metadata for an existing video."""
    metadata = {}
    if title:
        metadata['title'] = title
    if description:
        metadata['description'] = description
    if tags:
        metadata['tags'] = ','.join(tags) if isinstance(tags, list) else tags
    if channel:
        metadata['channel'] = channel
    
    return uploader.update_video(video_id, **metadata)

def get_channel_playlists(uploader, limit=50):
    """Get list of playlists in your channel."""
    uploader._refresh_token_if_needed()
    headers = {"Authorization": f"Bearer {uploader.auth_data['access_token']}"}
    
    try:
        response = uploader.session.get(
            f"{API_BASE_URL}/me/playlists",
            params={"limit": limit, "fields": "id,name,url"},
            headers=headers
        )
        response.raise_for_status()
        return response.json().get('list', [])
    except requests.RequestException as e:
        logger.error(f"Failed to get channel playlists: {e}")
        return []

def create_playlist(uploader, name, description=None, private=False):
    """Create a new playlist."""
    uploader._refresh_token_if_needed()
    headers = {"Authorization": f"Bearer {uploader.auth_data['access_token']}"}
    
    data = {
        'name': name,
        'private': str(private).lower()
    }
    if description:
        data['description'] = description
    
    try:
        response = uploader.session.post(
            f"{API_BASE_URL}/playlists",
            data=data,
            headers=headers
        )
        response.raise_for_status()
        playlist_info = response.json()
        logger.info(f"Created playlist: {name} (ID: {playlist_info['id']})")
        return playlist_info
    except requests.RequestException as e:
        logger.error(f"Failed to create playlist: {e}")
        return None

def add_video_to_playlist(uploader, playlist_id, video_id):
    """Add a video to a playlist."""
    uploader._refresh_token_if_needed()
    headers = {"Authorization": f"Bearer {uploader.auth_data['access_token']}"}
    
    try:
        response = uploader.session.post(
            f"{API_BASE_URL}/playlist/{playlist_id}/videos",
            data={'video_id': video_id},
            headers=headers
        )
        response.raise_for_status()
        logger.info(f"Added video {video_id} to playlist {playlist_id}")
        return True
    except requests.RequestException as e:
        logger.error(f"Failed to add video to playlist: {e}")
        return False

def get_video_analytics(uploader, video_id, period='month'):
    """Get analytics for a specific video."""
    uploader._refresh_token_if_needed()
    headers = {"Authorization": f"Bearer {uploader.auth_data['access_token']}"}
    
    try:
        response = uploader.session.get(
            f"{API_BASE_URL}/video/{video_id}/stats",
            params={'period': period},
            headers=headers
        )
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        logger.error(f"Failed to get video analytics: {e}")
        return None

def search_videos(uploader, query, limit=10, sort='recent'):
    """Search for videos on Dailymotion."""
    uploader._refresh_token_if_needed()
    headers = {"Authorization": f"Bearer {uploader.auth_data['access_token']}"}
    
    try:
        response = uploader.session.get(
            f"{API_BASE_URL}/videos",
            params={
                'search': query,
                'limit': limit,
                'sort': sort,
                'fields': 'id,title,url,owner.username'
            },
            headers=headers
        )
        response.raise_for_status()
        return response.json().get('list', [])
    except requests.RequestException as e:
        logger.error(f"Failed to search videos: {e}")
        return []

def monitor_upload_queue(directory, uploaded_log, interval=3600, max_runs=None):
    """Monitor a directory for new videos and upload them periodically."""
    runs = 0
    while max_runs is None or runs < max_runs:
        logger.info(f"Starting upload queue monitoring run #{runs+1}")
        
        try:
            # Create uploader
            uploader = DailymotionUploader()
            
            # Find videos to upload
            videos = find_videos_to_upload(directory, uploaded_log)
            
            if videos:
                logger.info(f"Found {len(videos)} new videos to upload")
                
                # Limit the number of videos to upload per run
                videos = videos[:UPLOAD_LIMIT_PER_RUN]
                
                # Upload videos
                batch_upload_videos(uploader, videos, uploaded_log, 
                                   tags=DEFAULT_TAGS, 
                                   channel=DEFAULT_CHANNEL)
                
                # Print upload statistics
                stats = uploader.get_upload_stats()
                logger.info(f"Upload statistics: {stats}")
            else:
                logger.info("No new videos found to upload")
        
        except Exception as e:
            logger.error(f"Error during monitoring run: {e}")
        
        runs += 1
        
        if max_runs is None or runs < max_runs:
            logger.info(f"Waiting {interval} seconds until next check...")
            time.sleep(interval)

def cleanup_failed_uploads(directory, uploaded_log, min_age_hours=24):
    """Find and retry failed uploads based on log file."""
    # Get list of all video files
    all_videos = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.lower().endswith(('.mp4', '.mkv', '.avi', '.mov', '.flv', '.webm')):
                all_videos.append(os.path.join(root, file))
    
    # Get list of successfully uploaded videos
    uploaded_videos = set()
    if os.path.exists(uploaded_log):
        with open(uploaded_log, 'r') as f:
            for line in f:
                if line.strip():
                    uploaded_videos.add(line.strip())
    
    # Find videos that exist but aren't in the uploaded log
    failed_uploads = []
    min_age_seconds = min_age_hours * 3600
    current_time = time.time()
    
    for video in all_videos:
        if video not in uploaded_videos:
            # Check if the file is old enough (to avoid picking up videos that are currently being processed)
            file_mod_time = os.path.getmtime(video)
            if (current_time - file_mod_time) >= min_age_seconds:
                failed_uploads.append(video)
    
    if failed_uploads:
        logger.info(f"Found {len(failed_uploads)} failed uploads to retry")
        
        # Create uploader and retry uploads
        uploader = DailymotionUploader()
        batch_upload_videos(uploader, failed_uploads, uploaded_log, 
                           tags=DEFAULT_TAGS, 
                           channel=DEFAULT_CHANNEL)
        
        # Print upload statistics
        stats = uploader.get_upload_stats()
        logger.info(f"Cleanup statistics: {stats}")
    else:
        logger.info("No failed uploads found to retry")

if __name__ == "__main__":
    main()

