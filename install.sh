#!/bin/bash

# YouTube to Dailymotion Pipeline Installation Script for VPS
# This script sets up the pipeline for 24/7 operation on a Linux VPS

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
INSTALL_DIR="/home/<USER>/youtube_pipeline"
SERVICE_NAME="youtube-pipeline"
USER="vps_user"

echo -e "${BLUE}YouTube to Dailymotion Pipeline Installation${NC}"
echo "=============================================="

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   echo -e "${RED}This script should not be run as root${NC}"
   echo "Please run as the vps_user or create a dedicated user"
   exit 1
fi

# Create installation directory
echo -e "${YELLOW}Creating installation directory...${NC}"
mkdir -p "$INSTALL_DIR"
cd "$INSTALL_DIR"

# Update system packages
echo -e "${YELLOW}Updating system packages...${NC}"
sudo apt update
sudo apt upgrade -y

# Install system dependencies
echo -e "${YELLOW}Installing system dependencies...${NC}"
sudo apt install -y \
    python3 \
    python3-pip \
    python3-venv \
    ffmpeg \
    git \
    curl \
    wget \
    unzip \
    htop \
    screen \
    supervisor \
    nginx \
    certbot \
    python3-certbot-nginx

# Install Python virtual environment
echo -e "${YELLOW}Setting up Python virtual environment...${NC}"
python3 -m venv venv
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install Python dependencies
echo -e "${YELLOW}Installing Python dependencies...${NC}"
pip install -r requirements.txt

# Create necessary directories
echo -e "${YELLOW}Creating directory structure...${NC}"
mkdir -p {downloads,modified_videos,logs,assets,config}

# Set up configuration files
echo -e "${YELLOW}Setting up configuration...${NC}"

# Create default config.json
cat > config/config.json << EOF
{
  "base_dir": "$INSTALL_DIR",
  "download_dir": "$INSTALL_DIR/downloads",
  "modified_dir": "$INSTALL_DIR/modified_videos",
  "log_dir": "$INSTALL_DIR/logs",
  "logo_path": "$INSTALL_DIR/assets/logo.png",
  "cookies_path": "$INSTALL_DIR/config/cookies.txt",
  "channels_file": "$INSTALL_DIR/config/channels.txt",
  "keywords_file": "$INSTALL_DIR/config/keywords.txt",
  "history_file": "$INSTALL_DIR/config/download_history.json",
  "uploaded_log": "$INSTALL_DIR/config/uploaded_videos.log",
  "token_file": "$INSTALL_DIR/config/token.json",
  
  "channel_name": "Fast in Sport",
  "dailymotion_channel": "sport",
  "dailymotion_tags": ["sports", "highlights", "fastinsport"],
  
  "speed_factor": 1.03,
  "brightness_factor": 1.08,
  "contrast_factor": 1.05,
  "logo_position": "bottom-right",
  "logo_size": 0.12,
  
  "upload_limit": 5,
  "download_limit": 10,
  "concurrent_uploads": 1,
  "max_video_size_gb": 2.0,
  "min_free_space_gb": 10.0,
  
  "run_interval_hours": 6,
  "download_schedule": "0 */6 * * *",
  "upload_schedule": "30 */3 * * *",
  "cleanup_schedule": "0 2 * * *",
  
  "max_retries": 3,
  "retry_delay": 300,
  "rate_limit_delay": 60,
  "health_check_interval": 300,
  
  "enable_monitoring": true,
  "log_level": "INFO",
  "max_log_files": 30,
  "max_log_size_mb": 100
}
EOF

# Create sample channels.txt
cat > config/channels.txt << EOF
# Add YouTube channel URLs here, one per line
# Examples:
# https://www.youtube.com/@espn
# https://www.youtube.com/@skysports
# https://www.youtube.com/c/NBCSports
EOF

# Create sample keywords.txt
cat > config/keywords.txt << EOF
# Add keywords to filter videos, one per line
# Examples:
highlights
goals
best moments
match
game
sport
football
soccer
basketball
tennis
EOF

# Create empty files
touch config/cookies.txt
touch config/download_history.json
touch config/uploaded_videos.log
echo '{}' > config/download_history.json

# Set up systemd service
echo -e "${YELLOW}Setting up systemd service...${NC}"
sudo tee /etc/systemd/system/${SERVICE_NAME}.service > /dev/null << EOF
[Unit]
Description=YouTube to Dailymotion Pipeline
After=network.target

[Service]
Type=simple
User=$USER
Group=$USER
WorkingDirectory=$INSTALL_DIR
Environment=PATH=$INSTALL_DIR/venv/bin
Environment=CONFIG_FILE=$INSTALL_DIR/config/config.json
ExecStart=$INSTALL_DIR/venv/bin/python daemon.py start
ExecStop=$INSTALL_DIR/venv/bin/python daemon.py stop
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=youtube-pipeline

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$INSTALL_DIR

[Install]
WantedBy=multi-user.target
EOF

# Set up log rotation
echo -e "${YELLOW}Setting up log rotation...${NC}"
sudo tee /etc/logrotate.d/youtube-pipeline > /dev/null << EOF
$INSTALL_DIR/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $USER $USER
    postrotate
        systemctl reload youtube-pipeline || true
    endscript
}
EOF

# Set up monitoring script
echo -e "${YELLOW}Setting up monitoring...${NC}"
cat > monitor.sh << 'EOF'
#!/bin/bash

# Simple monitoring script for the pipeline
INSTALL_DIR="/home/<USER>/youtube_pipeline"
LOG_FILE="$INSTALL_DIR/logs/monitor.log"

# Function to log with timestamp
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# Check if service is running
if ! systemctl is-active --quiet youtube-pipeline; then
    log "ERROR: Pipeline service is not running, attempting restart"
    systemctl restart youtube-pipeline
    sleep 10
    
    if systemctl is-active --quiet youtube-pipeline; then
        log "INFO: Pipeline service restarted successfully"
    else
        log "CRITICAL: Failed to restart pipeline service"
    fi
else
    log "INFO: Pipeline service is running normally"
fi

# Check disk space
DISK_USAGE=$(df "$INSTALL_DIR" | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 85 ]; then
    log "WARNING: Disk usage is high: ${DISK_USAGE}%"
fi

# Check memory usage
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ "$MEMORY_USAGE" -gt 90 ]; then
    log "WARNING: Memory usage is high: ${MEMORY_USAGE}%"
fi

log "INFO: Health check completed - Disk: ${DISK_USAGE}%, Memory: ${MEMORY_USAGE}%"
EOF

chmod +x monitor.sh

# Set up cron job for monitoring
echo -e "${YELLOW}Setting up monitoring cron job...${NC}"
(crontab -l 2>/dev/null; echo "*/5 * * * * $INSTALL_DIR/monitor.sh") | crontab -

# Set permissions
echo -e "${YELLOW}Setting file permissions...${NC}"
chmod +x daemon.py
chmod -R 755 "$INSTALL_DIR"
chmod 600 config/cookies.txt config/token.json 2>/dev/null || true

# Enable and start the service
echo -e "${YELLOW}Enabling and starting the service...${NC}"
sudo systemctl daemon-reload
sudo systemctl enable ${SERVICE_NAME}

echo -e "${GREEN}Installation completed successfully!${NC}"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "1. Add your YouTube cookies to: $INSTALL_DIR/config/cookies.txt"
echo "2. Add YouTube channel URLs to: $INSTALL_DIR/config/channels.txt"
echo "3. Configure Dailymotion API credentials in: $INSTALL_DIR/config/config.json"
echo "4. Add your logo image to: $INSTALL_DIR/assets/logo.png"
echo "5. Test the configuration: python daemon.py test"
echo "6. Start the service: sudo systemctl start ${SERVICE_NAME}"
echo ""
echo -e "${BLUE}Useful commands:${NC}"
echo "- Check service status: sudo systemctl status ${SERVICE_NAME}"
echo "- View logs: sudo journalctl -u ${SERVICE_NAME} -f"
echo "- Stop service: sudo systemctl stop ${SERVICE_NAME}"
echo "- Restart service: sudo systemctl restart ${SERVICE_NAME}"
echo ""
echo -e "${YELLOW}Important:${NC}"
echo "- Configure your Dailymotion API credentials before starting"
echo "- Make sure to add valid YouTube cookies for downloading"
echo "- Monitor the logs regularly for any issues"
echo "- The service will start automatically on system boot"
