"""
Advanced scheduler for 24/7 YouTube to Dailymotion pipeline operation
"""
import os
import sys
import time
import signal
import threading
import schedule
from datetime import datetime, timedelta
from typing import Dict, Any, Callable
import psutil
import json
from pathlib import Path

from config import config
from logger import get_logger

logger = get_logger('scheduler')

class PipelineScheduler:
    """Advanced scheduler with health monitoring and safety features"""
    
    def __init__(self):
        self.running = False
        self.jobs = {}
        self.health_stats = {}
        self.last_health_check = None
        self.shutdown_event = threading.Event()
        self.lock = threading.Lock()
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # Initialize health monitoring
        self._init_health_monitoring()
        
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.stop()
    
    def _init_health_monitoring(self):
        """Initialize health monitoring"""
        self.health_stats = {
            'start_time': datetime.now(),
            'total_runs': 0,
            'successful_runs': 0,
            'failed_runs': 0,
            'last_run_time': None,
            'last_success_time': None,
            'last_error_time': None,
            'consecutive_failures': 0,
            'system_stats': {},
            'disk_usage': {},
            'process_stats': {}
        }
    
    def add_job(self, name: str, func: Callable, schedule_expr: str, **kwargs):
        """Add a scheduled job"""
        try:
            # Parse schedule expression and add job
            if schedule_expr.startswith('*/'):
                # Handle */N format (every N minutes/hours)
                interval = int(schedule_expr.split('/')[1].split()[0])
                unit = schedule_expr.split()[1] if len(schedule_expr.split()) > 1 else 'minutes'
                
                if unit.startswith('minute'):
                    job = schedule.every(interval).minutes.do(self._run_job, name, func, **kwargs)
                elif unit.startswith('hour'):
                    job = schedule.every(interval).hours.do(self._run_job, name, func, **kwargs)
                else:
                    raise ValueError(f"Unsupported unit: {unit}")
            else:
                # Handle cron-like expressions (simplified)
                parts = schedule_expr.split()
                if len(parts) == 5:
                    minute, hour, day, month, weekday = parts
                    
                    if hour != '*' and minute != '*':
                        time_str = f"{hour.zfill(2)}:{minute.zfill(2)}"
                        job = schedule.every().day.at(time_str).do(self._run_job, name, func, **kwargs)
                    elif minute != '*' and hour == '*':
                        # Every hour at specific minute
                        job = schedule.every().hour.at(f":{minute.zfill(2)}").do(self._run_job, name, func, **kwargs)
                    else:
                        raise ValueError(f"Unsupported cron expression: {schedule_expr}")
                else:
                    raise ValueError(f"Invalid cron expression: {schedule_expr}")
            
            self.jobs[name] = {
                'job': job,
                'function': func,
                'schedule': schedule_expr,
                'kwargs': kwargs,
                'last_run': None,
                'last_success': None,
                'last_error': None,
                'run_count': 0,
                'success_count': 0,
                'error_count': 0
            }
            
            logger.info(f"Added job '{name}' with schedule '{schedule_expr}'")
            
        except Exception as e:
            logger.error(f"Failed to add job '{name}': {e}")
            raise
    
    def _run_job(self, name: str, func: Callable, **kwargs):
        """Execute a scheduled job with error handling and monitoring"""
        if self.shutdown_event.is_set():
            return
        
        start_time = datetime.now()
        
        with self.lock:
            self.jobs[name]['last_run'] = start_time
            self.jobs[name]['run_count'] += 1
            self.health_stats['total_runs'] += 1
        
        logger.info(f"Starting job '{name}'")
        
        try:
            # Check system health before running job
            if not self._check_system_health():
                logger.warning(f"System health check failed, skipping job '{name}'")
                return
            
            # Run the job
            result = func(**kwargs)
            
            # Record success
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            with self.lock:
                self.jobs[name]['last_success'] = end_time
                self.jobs[name]['success_count'] += 1
                self.health_stats['successful_runs'] += 1
                self.health_stats['last_success_time'] = end_time
                self.health_stats['consecutive_failures'] = 0
            
            logger.info(f"Job '{name}' completed successfully in {duration:.2f} seconds")
            
            # Log job statistics
            logger.log_operation(
                operation=f"scheduled_job_{name}",
                status="success",
                duration=duration,
                result=result
            )
            
        except Exception as e:
            # Record failure
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            with self.lock:
                self.jobs[name]['last_error'] = end_time
                self.jobs[name]['error_count'] += 1
                self.health_stats['failed_runs'] += 1
                self.health_stats['last_error_time'] = end_time
                self.health_stats['consecutive_failures'] += 1
            
            logger.error(f"Job '{name}' failed after {duration:.2f} seconds: {e}")
            logger.exception(f"Job '{name}' exception details")
            
            # Log job failure
            logger.log_operation(
                operation=f"scheduled_job_{name}",
                status="failed",
                duration=duration,
                error=str(e)
            )
            
            # Check if we need to take action on consecutive failures
            if self.health_stats['consecutive_failures'] >= config.get('max_consecutive_failures', 5):
                logger.critical(f"Too many consecutive failures ({self.health_stats['consecutive_failures']}), system may need attention")
    
    def _check_system_health(self) -> bool:
        """Check system health before running jobs"""
        try:
            # Check disk space
            if not config.check_disk_space():
                logger.warning("Insufficient disk space")
                return False
            
            # Check memory usage
            memory = psutil.virtual_memory()
            if memory.percent > config.get('max_memory_usage', 90):
                logger.warning(f"High memory usage: {memory.percent}%")
                return False
            
            # Check CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > config.get('max_cpu_usage', 95):
                logger.warning(f"High CPU usage: {cpu_percent}%")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False
    
    def _update_system_stats(self):
        """Update system statistics"""
        try:
            # CPU and Memory
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # Disk usage
            base_dir = config.get('base_dir')
            disk_usage = config.get_disk_usage(base_dir)
            
            # Process info
            process = psutil.Process()
            process_info = {
                'pid': process.pid,
                'memory_mb': process.memory_info().rss / 1024 / 1024,
                'cpu_percent': process.cpu_percent(),
                'num_threads': process.num_threads(),
                'create_time': datetime.fromtimestamp(process.create_time()).isoformat()
            }
            
            self.health_stats['system_stats'] = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_gb': memory.available / 1024**3,
                'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else None,
                'uptime_hours': (datetime.now() - self.health_stats['start_time']).total_seconds() / 3600
            }
            
            self.health_stats['disk_usage'] = disk_usage
            self.health_stats['process_stats'] = process_info
            
            # Log system stats periodically
            logger.log_system_stats(**self.health_stats['system_stats'])
            
        except Exception as e:
            logger.error(f"Failed to update system stats: {e}")
    
    def _health_check_worker(self):
        """Background worker for health monitoring"""
        while not self.shutdown_event.is_set():
            try:
                self._update_system_stats()
                self.last_health_check = datetime.now()
                
                # Save health stats to file
                self._save_health_stats()
                
                # Wait for next check
                self.shutdown_event.wait(config.get('health_check_interval', 300))
                
            except Exception as e:
                logger.error(f"Health check worker error: {e}")
                self.shutdown_event.wait(60)  # Wait 1 minute before retry
    
    def _save_health_stats(self):
        """Save health statistics to file"""
        try:
            stats_file = Path(config.get('base_dir')) / 'health_stats.json'
            
            # Convert datetime objects to strings for JSON serialization
            stats_copy = self.health_stats.copy()
            for key, value in stats_copy.items():
                if isinstance(value, datetime):
                    stats_copy[key] = value.isoformat()
            
            # Add job statistics
            stats_copy['jobs'] = {}
            for name, job_info in self.jobs.items():
                stats_copy['jobs'][name] = {
                    'schedule': job_info['schedule'],
                    'run_count': job_info['run_count'],
                    'success_count': job_info['success_count'],
                    'error_count': job_info['error_count'],
                    'last_run': job_info['last_run'].isoformat() if job_info['last_run'] else None,
                    'last_success': job_info['last_success'].isoformat() if job_info['last_success'] else None,
                    'last_error': job_info['last_error'].isoformat() if job_info['last_error'] else None
                }
            
            with open(stats_file, 'w') as f:
                json.dump(stats_copy, f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to save health stats: {e}")
    
    def start(self):
        """Start the scheduler"""
        if self.running:
            logger.warning("Scheduler is already running")
            return
        
        self.running = True
        logger.info("Starting pipeline scheduler...")
        
        # Start health monitoring thread
        health_thread = threading.Thread(target=self._health_check_worker, daemon=True)
        health_thread.start()
        
        # Main scheduler loop
        try:
            while self.running and not self.shutdown_event.is_set():
                schedule.run_pending()
                time.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        except Exception as e:
            logger.error(f"Scheduler error: {e}")
            logger.exception("Scheduler exception details")
        finally:
            self.stop()
    
    def stop(self):
        """Stop the scheduler gracefully"""
        if not self.running:
            return
        
        logger.info("Stopping pipeline scheduler...")
        self.running = False
        self.shutdown_event.set()
        
        # Save final health stats
        self._save_health_stats()
        
        logger.info("Pipeline scheduler stopped")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current scheduler status"""
        return {
            'running': self.running,
            'jobs_count': len(self.jobs),
            'health_stats': self.health_stats,
            'last_health_check': self.last_health_check.isoformat() if self.last_health_check else None,
            'jobs': {name: {
                'schedule': info['schedule'],
                'run_count': info['run_count'],
                'success_count': info['success_count'],
                'error_count': info['error_count'],
                'last_run': info['last_run'].isoformat() if info['last_run'] else None
            } for name, info in self.jobs.items()}
        }

# Global scheduler instance
scheduler = PipelineScheduler()
