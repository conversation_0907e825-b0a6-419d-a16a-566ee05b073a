import os
import sys
import subprocess
import random
import argparse
from moviepy.editor import Video<PERSON><PERSON><PERSON><PERSON>, ImageClip, CompositeVideoClip, concatenate_videoclips, TextClip, ColorClip

# ===== CONFIGURATION - MODIFY THESE PATHS =====
INPUT_VIDEO_PATH = "D:/youtube acript/dailymotion to youtube/video file/Best moments of the day #15 ｜ Roland-Garros 2025.mp4"  # Path to input video file
OUTPUT_DIRECTORY = "D:/youtube acript/dailymotion to youtube/output"  # Directory to save modified videos
LOGO_IMAGE_PATH = "D:/youtube acript/dailymotion to youtube/video file/fast in sport logo.png"  # Path to logo image
CHANNEL_NAME = "Fast in Sport"  # Your channel name to display

# ===== VIDEO MODIFICATION SETTINGS =====
SPEED_FACTOR = 1.03  # Speed adjustment (subtle 3% increase - less noticeable but still effective)
BRIGHTNESS_FACTOR = 1.08  # Brightness adjustment (8% brighter - noticeable improvement without looking artificial)
CONTRAST_FACTOR = 1.05  # Contrast adjustment (5% more contrast - enhances video without looking over-processed)
LOGO_POSITION = 'top-right'  # Options: 'bottom-right', 'bottom-left', 'top-right', 'top-left', 'center'
LOGO_SIZE = 0.20  # 12% of video height - visible but not distracting

# ===== LOGO POSITION OPTIONS =====
# 'bottom-right' - Standard position, least likely to interfere with content
# 'bottom-left' - Good alternative if score/graphics appear in bottom right
# 'top-right' - Useful for sports where bottom areas show important graphics
# 'top-left' - Less common but can work well for certain content types
# 'center' - Maximum visibility but most intrusive (use sparingly)
# 'random' - Randomly selects one of the above positions for each video
# =========================================

# ===== PERFORMANCE SETTINGS =====
BITRATE = "8000k"  # Higher bitrate for better quality (adjust based on your needs)
THREADS = 4  # Number of threads to use for encoding (increase for faster processing)
PRESET = "faster"  # ffmpeg preset (options: ultrafast, superfast, veryfast, faster, fast, medium, slow, slower, veryslow)
# =========================================

def modify_video(input_path, output_path, logo_path=None, channel_name=None,
                 speed_factor=1.05, brightness_factor=1.1, contrast_factor=1.1,
                 logo_position='bottom-right', logo_size=0.15, bitrate="8000k", threads=4, preset="faster"):
    """
    Modify a video by:
    1. Adding a logo watermark
    2. Adding channel name text
    3. Slightly changing speed, brightness, and contrast
    4. Randomly trimming a few seconds from beginning and end
    """
    print(f"Processing video: {input_path}")
    
    try:
        # Load the main video with lower resolution for faster processing
        main_video = VideoFileClip(input_path)
        
        # Get original video resolution and bitrate for output
        original_width = main_video.w
        original_height = main_video.h
        
        # Random trim (1-3 seconds from beginning and end)
        start_trim = random.uniform(1, 3)
        end_trim = random.uniform(1, 3)
        duration = main_video.duration
        
        if duration > 10:  # Only trim if video is long enough
            main_video = main_video.subclip(start_trim, duration - end_trim)
            print(f"Trimmed {start_trim:.1f}s from start and {end_trim:.1f}s from end")
        
        # Speed adjustment (makes audio pitch higher too)
        main_video = main_video.fx(VideoFileClip.speedx, speed_factor)
        print(f"Adjusted speed by factor of {speed_factor}")
        
        # Create a list of clips to composite
        clips = [main_video]
        
        # Apply logo if provided
        if logo_path and os.path.exists(logo_path):
            # Handle random logo position if specified
            if logo_position.lower() == 'random':
                positions = ['bottom-right', 'bottom-left', 'top-right', 'top-left', 'center']
                logo_position = random.choice(positions)
                print(f"Randomly selected logo position: {logo_position}")
            
            # Set logo position
            logo_position = logo_position.lower()  # Normalize to lowercase
            if logo_position == 'bottom-right':
                pos = ('right', 'bottom')
                margin = (20, 20)  # Add some margin from the edges
            elif logo_position == 'bottom-left':
                pos = ('left', 'bottom')
                margin = (20, 20)
            elif logo_position == 'top-right':
                pos = ('right', 'top')
                margin = (20, 20)
            elif logo_position == 'top-left':
                pos = ('left', 'top')
                margin = (20, 20)
            elif logo_position == 'center':
                pos = ('center', 'center')
                margin = (0, 0)
            else:
                print(f"Unknown logo position '{logo_position}', defaulting to bottom-right")
                pos = ('right', 'bottom')
                margin = (20, 20)
            
            try:
                # Load and set up logo
                logo = (ImageClip(logo_path)
                        .set_duration(main_video.duration)
                        .resize(height=main_video.h * logo_size)  # Resize logo
                        .margin(opacity=0, left=margin[0], right=margin[0], 
                                top=margin[1], bottom=margin[1])  # Add margin
                        .set_pos(pos))  # Position logo
                
                # Add logo to clips list
                clips.append(logo)
                print(f"Added logo from {logo_path} at position {logo_position}")
            except Exception as e:
                print(f"Error adding logo: {e}")
        
        # Add channel name text if provided
        if channel_name:
            try:
                # Create text clip with channel name
                txt_clip = (TextClip(channel_name, fontsize=main_video.h * 0.05, color='white', 
                                    font='Arial-Bold', stroke_color='black', stroke_width=2)
                           .set_duration(main_video.duration)
                           .margin(opacity=0, left=20, top=20)  # Add margin from edge
                           .set_pos(('left', 'top')))
                
                # Add text to clips list
                clips.append(txt_clip)
                print(f"Added channel name: {channel_name}")
            except Exception as e:
                print(f"Error adding channel name: {e}")
        
        # Composite all clips
        final_video = CompositeVideoClip(clips)
        
        # Apply brightness and contrast adjustments
        final_video = final_video.fx(lambda clip: clip.fl_image(
            lambda img: (img * brightness_factor).clip(0, 255).astype('uint8')))
        
        print(f"Adjusted brightness by factor of {brightness_factor}")
        print(f"Total duration: {final_video.duration:.1f}s")
        
        # Write the result with optimized settings
        final_video.write_videofile(
            output_path, 
            codec='libx264', 
            audio_codec='aac',
            bitrate=bitrate,
            threads=threads,
            preset=preset,
            ffmpeg_params=["-crf", "18"]  # Lower CRF = higher quality (18 is visually lossless)
        )
        
        # Close all clips to release resources
        for clip in clips:
            clip.close()
        final_video.close()
        
        print(f"Successfully created modified video: {output_path}")
        return True
        
    except Exception as e:
        print(f"Error modifying video: {e}")
        return False

def process_directory(input_dir, output_dir, logo_path=None, channel_name=None,
                     speed_factor=1.05, brightness_factor=1.1, contrast_factor=1.1,
                     logo_position='bottom-right', logo_size=0.15, bitrate="8000k", threads=4, preset="faster"):
    """Process all video files in a directory."""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        
    video_extensions = ['.mp4', '.avi', '.mkv', '.mov', '.flv', '.wmv']
    videos_processed = 0
    
    for filename in os.listdir(input_dir):
        file_path = os.path.join(input_dir, filename)
        if os.path.isfile(file_path) and os.path.splitext(filename)[1].lower() in video_extensions:
            output_path = os.path.join(output_dir, filename)  # Keep original filename
            
            if modify_video(file_path, output_path, logo_path, channel_name,
                           speed_factor, brightness_factor, contrast_factor,
                           logo_position, logo_size, bitrate, threads, preset):
                videos_processed += 1
    
    print(f"Processed {videos_processed} videos")

if __name__ == "__main__":
    # Create output directory if it doesn't exist
    if not os.path.exists(OUTPUT_DIRECTORY):
        os.makedirs(OUTPUT_DIRECTORY)
    
    # Check if input is a directory or a single file
    if os.path.isdir(INPUT_VIDEO_PATH):
        process_directory(INPUT_VIDEO_PATH, OUTPUT_DIRECTORY, 
                         LOGO_IMAGE_PATH, CHANNEL_NAME,
                         SPEED_FACTOR, BRIGHTNESS_FACTOR, CONTRAST_FACTOR,
                         LOGO_POSITION, LOGO_SIZE, BITRATE, THREADS, PRESET)
    else:
        # Get the filename from the input path
        filename = os.path.basename(INPUT_VIDEO_PATH)
        output_path = os.path.join(OUTPUT_DIRECTORY, filename)
        
        modify_video(INPUT_VIDEO_PATH, output_path, 
                    LOGO_IMAGE_PATH, CHANNEL_NAME,
                    SPEED_FACTOR, BRIGHTNESS_FACTOR, CONTRAST_FACTOR,
                    LOGO_POSITION, LOGO_SIZE, BITRATE, THREADS, PRESET)

# Make these functions available for import by other scripts
__all__ = ['modify_video', 'process_directory']





