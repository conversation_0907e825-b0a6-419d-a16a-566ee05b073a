"""
Isolated configuration for multi-project VPS environment
Ensures no interference with aapanel, RDP, or other services
"""
import os
import json
import psutil
from pathlib import Path
from typing import Dict, Any

class IsolatedConfig:
    """Configuration manager designed for multi-project VPS isolation"""
    
    def __init__(self, config_file: str = None):
        self.config_file = config_file or os.getenv('CONFIG_FILE', '/opt/youtube-dailymotion-pipeline/config/config.json')
        self.config = self._load_config()
        
        # Ensure isolation settings
        self._apply_isolation_settings()
        
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration with isolation defaults"""
        config = {}
        
        # Try to load from config file
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
            except Exception as e:
                print(f"Warning: Could not load config file {self.config_file}: {e}")
        
        # Override with environment variables (prefixed to avoid conflicts)
        config.update(self._get_isolated_env_config())
        
        # Set isolation-safe defaults
        config = self._set_isolation_defaults(config)
        
        return config
    
    def _get_isolated_env_config(self) -> Dict[str, Any]:
        """Get configuration from environment variables with YT_PIPELINE_ prefix"""
        return {
            # Paths (all isolated)
            'base_dir': os.getenv('YT_PIPELINE_BASE_DIR', '/opt/youtube-dailymotion-pipeline'),
            'download_dir': os.getenv('YT_PIPELINE_DOWNLOAD_DIR', '/opt/youtube-dailymotion-pipeline/downloads'),
            'modified_dir': os.getenv('YT_PIPELINE_MODIFIED_DIR', '/opt/youtube-dailymotion-pipeline/modified_videos'),
            'logo_path': os.getenv('YT_PIPELINE_LOGO_PATH', '/opt/youtube-dailymotion-pipeline/assets/logo.png'),
            'log_dir': os.getenv('YT_PIPELINE_LOG_DIR', '/opt/youtube-dailymotion-pipeline/logs'),
            
            # Resource limits (conservative for multi-project VPS)
            'max_memory_usage_percent': int(os.getenv('YT_PIPELINE_MAX_MEMORY', '40')),  # Max 40% of system memory
            'max_cpu_usage_percent': int(os.getenv('YT_PIPELINE_MAX_CPU', '60')),       # Max 60% CPU usage
            'max_disk_usage_percent': int(os.getenv('YT_PIPELINE_MAX_DISK', '70')),     # Max 70% disk usage
            'min_free_space_gb': float(os.getenv('YT_PIPELINE_MIN_FREE_SPACE', '5.0')), # Keep 5GB free
            
            # Processing limits (reduced for shared environment)
            'upload_limit': int(os.getenv('YT_PIPELINE_UPLOAD_LIMIT', '3')),            # Max 3 uploads per run
            'download_limit': int(os.getenv('YT_PIPELINE_DOWNLOAD_LIMIT', '5')),        # Max 5 downloads per run
            'concurrent_uploads': int(os.getenv('YT_PIPELINE_CONCURRENT_UPLOADS', '1')), # Sequential only
            'max_video_size_gb': float(os.getenv('YT_PIPELINE_MAX_VIDEO_SIZE', '1.0')), # Max 1GB per video
            
            # Timing (spread out to avoid resource conflicts)
            'run_interval_hours': int(os.getenv('YT_PIPELINE_RUN_INTERVAL', '8')),      # Every 8 hours
            'rate_limit_delay': int(os.getenv('YT_PIPELINE_RATE_DELAY', '180')),        # 3 minutes between ops
            'health_check_interval': int(os.getenv('YT_PIPELINE_HEALTH_INTERVAL', '600')), # 10 minutes
            
            # Network (isolated ports)
            'dashboard_host': os.getenv('YT_PIPELINE_DASHBOARD_HOST', '127.0.0.1'),     # Localhost only
            'dashboard_port': int(os.getenv('YT_PIPELINE_DASHBOARD_PORT', '8090')),     # Avoid common ports
            'monitoring_port': int(os.getenv('YT_PIPELINE_MONITORING_PORT', '9091')),   # Avoid common ports
            
            # Logging (conservative)
            'log_level': os.getenv('YT_PIPELINE_LOG_LEVEL', 'INFO'),
            'max_log_files': int(os.getenv('YT_PIPELINE_MAX_LOG_FILES', '10')),
            'max_log_size_mb': int(os.getenv('YT_PIPELINE_MAX_LOG_SIZE', '25')),        # Smaller log files
        }
    
    def _set_isolation_defaults(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Set safe defaults for multi-project environment"""
        defaults = {
            # Conservative scheduling (avoid peak hours)
            'download_schedule': '0 2,10,18 * * *',    # 2 AM, 10 AM, 6 PM
            'upload_schedule': '30 4,12,20 * * *',     # 4:30 AM, 12:30 PM, 8:30 PM
            'cleanup_schedule': '0 1 * * *',           # 1 AM daily
            
            # Resource-conscious settings
            'max_retries': 2,                          # Fewer retries
            'retry_delay': 600,                        # 10 minutes between retries
            'process_timeout': 1800,                   # 30 minutes max per operation
            
            # Safety settings
            'enable_resource_monitoring': True,
            'auto_pause_on_high_load': True,
            'respect_system_limits': True,
            
            # Isolation settings
            'use_nice_priority': True,                 # Lower process priority
            'ionice_class': 3,                         # Idle I/O priority
            'temp_dir': '/tmp/yt-pipeline',            # Isolated temp directory
        }
        
        for key, value in defaults.items():
            if key not in config:
                config[key] = value
                
        return config
    
    def _apply_isolation_settings(self):
        """Apply system-level isolation settings"""
        try:
            # Set process priority to be nice to other processes
            if self.get('use_nice_priority', True):
                os.nice(10)  # Lower priority
            
            # Set I/O priority to idle (Linux only)
            if hasattr(os, 'system') and self.get('ionice_class'):
                os.system(f"ionice -c {self.get('ionice_class')} -p {os.getpid()} 2>/dev/null || true")
            
        except Exception as e:
            print(f"Warning: Could not apply isolation settings: {e}")
    
    def check_system_resources(self) -> Dict[str, bool]:
        """Check if system resources allow pipeline operation"""
        checks = {
            'memory_ok': True,
            'cpu_ok': True,
            'disk_ok': True,
            'load_ok': True
        }
        
        try:
            # Memory check
            memory = psutil.virtual_memory()
            max_memory = self.get('max_memory_usage_percent', 40)
            if memory.percent > max_memory:
                checks['memory_ok'] = False
            
            # CPU check
            cpu_percent = psutil.cpu_percent(interval=1)
            max_cpu = self.get('max_cpu_usage_percent', 60)
            if cpu_percent > max_cpu:
                checks['cpu_ok'] = False
            
            # Disk check
            base_dir = self.get('base_dir')
            disk_usage = self.get_disk_usage(base_dir)
            max_disk = self.get('max_disk_usage_percent', 70)
            if disk_usage.get('usage_percent', 0) > max_disk:
                checks['disk_ok'] = False
            
            # Load average check (Linux only)
            if hasattr(os, 'getloadavg'):
                load_avg = os.getloadavg()[0]  # 1-minute load average
                cpu_count = psutil.cpu_count()
                if load_avg > cpu_count * 0.8:  # 80% of CPU cores
                    checks['load_ok'] = False
            
        except Exception as e:
            print(f"Warning: Resource check failed: {e}")
        
        return checks
    
    def should_pause_operations(self) -> bool:
        """Check if operations should be paused due to system load"""
        if not self.get('auto_pause_on_high_load', True):
            return False
        
        resource_checks = self.check_system_resources()
        
        # Pause if any critical resource is overloaded
        critical_resources = ['memory_ok', 'cpu_ok', 'load_ok']
        for resource in critical_resources:
            if not resource_checks.get(resource, True):
                return True
        
        return False
    
    def get_safe_processing_limits(self) -> Dict[str, int]:
        """Get processing limits based on current system load"""
        base_limits = {
            'upload_limit': self.get('upload_limit', 3),
            'download_limit': self.get('download_limit', 5),
            'concurrent_uploads': self.get('concurrent_uploads', 1)
        }
        
        # Reduce limits if system is under load
        resource_checks = self.check_system_resources()
        
        # If memory or CPU is high, reduce limits by 50%
        if not resource_checks.get('memory_ok') or not resource_checks.get('cpu_ok'):
            for key in base_limits:
                base_limits[key] = max(1, base_limits[key] // 2)
        
        return base_limits
    
    def get_isolated_ports(self) -> Dict[str, int]:
        """Get available ports that don't conflict with common services"""
        # Common ports used by aapanel, RDP, web servers, etc.
        reserved_ports = {
            22, 80, 443, 3389,      # SSH, HTTP, HTTPS, RDP
            8080, 8081, 8888, 8889,  # Common web panels
            3306, 5432, 6379,        # Databases
            21, 22, 25, 53, 110, 143, 993, 995,  # Standard services
            7800, 888, 8888, 8080    # aapanel common ports
        }
        
        dashboard_port = self.get('dashboard_port', 8090)
        monitoring_port = self.get('monitoring_port', 9091)
        
        # Find alternative ports if current ones are in use
        import socket
        
        def is_port_available(port):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('127.0.0.1', port))
                    return True
            except:
                return False
        
        # Find available dashboard port
        while dashboard_port in reserved_ports or not is_port_available(dashboard_port):
            dashboard_port += 1
            if dashboard_port > 9999:
                dashboard_port = 8090
                break
        
        # Find available monitoring port
        while monitoring_port in reserved_ports or not is_port_available(monitoring_port) or monitoring_port == dashboard_port:
            monitoring_port += 1
            if monitoring_port > 9999:
                monitoring_port = 9091
                break
        
        return {
            'dashboard_port': dashboard_port,
            'monitoring_port': monitoring_port
        }
    
    def create_isolated_directories(self):
        """Create directories with proper isolation"""
        dirs_to_create = [
            'base_dir', 'download_dir', 'modified_dir', 'log_dir'
        ]
        
        for dir_key in dirs_to_create:
            dir_path = self.get(dir_key)
            if dir_path:
                Path(dir_path).mkdir(parents=True, exist_ok=True, mode=0o755)
        
        # Create isolated temp directory
        temp_dir = self.get('temp_dir', '/tmp/yt-pipeline')
        Path(temp_dir).mkdir(parents=True, exist_ok=True, mode=0o755)
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        return self.config.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """Set configuration value"""
        self.config[key] = value
    
    def get_disk_usage(self, path: str) -> Dict[str, float]:
        """Get disk usage information in GB"""
        try:
            stat = os.statvfs(path)
            total = (stat.f_blocks * stat.f_frsize) / (1024**3)
            free = (stat.f_bavail * stat.f_frsize) / (1024**3)
            used = total - free
            
            return {
                'total_gb': round(total, 2),
                'used_gb': round(used, 2),
                'free_gb': round(free, 2),
                'usage_percent': round((used / total) * 100, 2)
            }
        except Exception as e:
            print(f"Error getting disk usage for {path}: {e}")
            return {'total_gb': 0, 'used_gb': 0, 'free_gb': 0, 'usage_percent': 0}
    
    def validate_isolation(self) -> bool:
        """Validate that isolation settings are properly configured"""
        required_settings = [
            'base_dir', 'download_dir', 'modified_dir', 'log_dir'
        ]
        
        for setting in required_settings:
            if not self.get(setting):
                print(f"Missing required isolation setting: {setting}")
                return False
        
        # Check that we're not using system directories
        base_dir = self.get('base_dir')
        if base_dir in ['/', '/usr', '/var', '/etc', '/home']:
            print(f"Invalid base directory for isolation: {base_dir}")
            return False
        
        return True

# Global isolated config instance
config = IsolatedConfig()
