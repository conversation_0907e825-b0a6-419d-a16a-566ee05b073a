# YouTube to Dailymotion Pipeline - 24/7 VPS Edition

A robust, production-ready pipeline that automatically downloads sports videos from YouTube, modifies them to avoid copyright detection, and uploads them to Dailymotion. Designed for 24/7 operation on VPS with comprehensive monitoring and safety features.

## 🚀 Features

### Core Functionality
- **Automated Video Download**: Downloads videos from specified YouTube channels based on keywords
- **Video Modification**: Applies speed changes, brightness/contrast adjustments, logo watermarks, and random trimming
- **Automated Upload**: Uploads modified videos to Dailymotion with proper metadata
- **Duplicate Prevention**: Tracks downloaded and uploaded videos to prevent duplicates

### 24/7 Operation Features
- **Scheduled Execution**: Configurable cron-like scheduling for different operations
- **Health Monitoring**: System resource monitoring with alerts
- **Automatic Recovery**: Retry mechanisms and error recovery
- **Graceful Shutdown**: Proper signal handling for safe restarts
- **Resource Management**: Disk space monitoring and automatic cleanup
- **Comprehensive Logging**: Structured logging with rotation and monitoring

### Safety Features
- **Rate Limiting**: Configurable delays between operations
- **Resource Limits**: CPU, memory, and disk usage monitoring
- **Error Handling**: Comprehensive error handling with notifications
- **Configuration Validation**: Startup validation of all settings
- **Backup Systems**: Health checks and automatic service recovery

## 📋 Prerequisites

### System Requirements
- **OS**: Linux (Ubuntu 20.04+ recommended)
- **RAM**: Minimum 2GB, recommended 4GB+
- **Storage**: Minimum 50GB free space
- **CPU**: 2+ cores recommended
- **Network**: Stable internet connection

### Required Accounts
- **YouTube**: Account with cookies for downloading (optional but recommended)
- **Dailymotion**: Developer account with API credentials

## 🛠️ Installation Methods

### Method 1: Docker (Recommended)

1. **Clone the repository**:
```bash
git clone <repository-url>
cd youtube-dailymotion-pipeline
```

2. **Create configuration directories**:
```bash
mkdir -p {config,downloads,modified_videos,logs,assets}
```

3. **Configure the pipeline**:
```bash
# Copy sample configuration
cp docker/config.sample.json config/config.json

# Edit configuration with your settings
nano config/config.json
```

4. **Add required files**:
```bash
# Add YouTube cookies (export from browser)
nano config/cookies.txt

# Add YouTube channel URLs
nano config/channels.txt

# Add keywords for filtering
nano config/keywords.txt

# Add your logo image
cp your-logo.png assets/logo.png
```

5. **Start the pipeline**:
```bash
# Build and start
docker-compose up -d

# Check logs
docker-compose logs -f youtube-pipeline
```

### Method 2: Direct VPS Installation

1. **Run the installation script**:
```bash
wget https://raw.githubusercontent.com/your-repo/install.sh
chmod +x install.sh
./install.sh
```

2. **Configure the pipeline**:
```bash
cd /home/<USER>/youtube_pipeline

# Edit configuration
nano config/config.json

# Add required files
nano config/cookies.txt
nano config/channels.txt
nano config/keywords.txt
```

3. **Start the service**:
```bash
# Test configuration
python daemon.py test

# Start the service
sudo systemctl start youtube-pipeline

# Enable auto-start
sudo systemctl enable youtube-pipeline
```

## ⚙️ Configuration

### Main Configuration (`config/config.json`)

```json
{
  "base_dir": "/app",
  "download_dir": "/app/downloads",
  "modified_dir": "/app/modified_videos",
  "log_dir": "/app/logs",
  
  "channel_name": "Your Channel Name",
  "dailymotion_channel": "sport",
  "dailymotion_tags": ["sports", "highlights"],
  
  "dailymotion_client_id": "your_client_id",
  "dailymotion_client_secret": "your_client_secret",
  "dailymotion_username": "your_username",
  "dailymotion_password": "your_password",
  
  "speed_factor": 1.03,
  "brightness_factor": 1.08,
  "contrast_factor": 1.05,
  "logo_position": "bottom-right",
  "logo_size": 0.12,
  
  "upload_limit": 5,
  "download_limit": 10,
  "max_video_size_gb": 2.0,
  "min_free_space_gb": 10.0,
  
  "download_schedule": "0 */6 * * *",
  "upload_schedule": "30 */3 * * *",
  "cleanup_schedule": "0 2 * * *",
  
  "enable_monitoring": true,
  "webhook_url": "https://your-webhook-url.com",
  "email_notifications": true,
  "notification_email": "<EMAIL>"
}
```

### Channel Configuration (`config/channels.txt`)
```
https://www.youtube.com/@espn
https://www.youtube.com/@skysports
https://www.youtube.com/c/NBCSports
```

### Keywords Configuration (`config/keywords.txt`)
```
highlights
goals
best moments
match highlights
game recap
sport
football
soccer
basketball
tennis
```

## 🔧 Usage

### Docker Commands
```bash
# Start the pipeline
docker-compose up -d

# Stop the pipeline
docker-compose down

# View logs
docker-compose logs -f youtube-pipeline

# Check status
docker-compose exec youtube-pipeline python daemon.py status

# Run test
docker-compose exec youtube-pipeline python daemon.py test

# Access shell
docker-compose exec youtube-pipeline bash
```

### Systemd Commands (Direct Installation)
```bash
# Start service
sudo systemctl start youtube-pipeline

# Stop service
sudo systemctl stop youtube-pipeline

# Restart service
sudo systemctl restart youtube-pipeline

# Check status
sudo systemctl status youtube-pipeline

# View logs
sudo journalctl -u youtube-pipeline -f

# Check daemon status
python daemon.py status
```

## 📊 Monitoring

### Health Checks
The pipeline includes comprehensive health monitoring:

- **System Resources**: CPU, memory, disk usage
- **Process Health**: Service status and responsiveness
- **Operation Success**: Download, modification, and upload success rates
- **Error Tracking**: Consecutive failures and error patterns

### Log Files
- `logs/main.log`: Main application logs
- `logs/main_errors.log`: Error-specific logs
- `logs/main_structured.log`: JSON structured logs for monitoring
- `logs/monitor.log`: Health check logs

### Notifications
Configure notifications via:
- **Webhooks**: Send alerts to Slack, Discord, etc.
- **Email**: SMTP email notifications
- **Log Monitoring**: Structured logs for external monitoring systems

## 🔒 Security Considerations

### File Permissions
```bash
# Secure sensitive files
chmod 600 config/cookies.txt
chmod 600 config/token.json
chmod 600 config/config.json
```

### Network Security
- Use HTTPS for all API communications
- Implement firewall rules to restrict access
- Use VPN for remote access to VPS

### API Security
- Store API credentials securely
- Use environment variables for sensitive data
- Regularly rotate API keys and passwords

## 🚨 Troubleshooting

### Common Issues

1. **Service won't start**:
```bash
# Check configuration
python daemon.py test

# Check logs
sudo journalctl -u youtube-pipeline -n 50
```

2. **Downloads failing**:
```bash
# Update yt-dlp
pip install --upgrade yt-dlp

# Check cookies
# Export fresh cookies from browser
```

3. **Uploads failing**:
```bash
# Check Dailymotion API credentials
# Verify token.json is valid
# Check rate limits
```

4. **High resource usage**:
```bash
# Check disk space
df -h

# Check memory usage
free -h

# Check running processes
htop
```

### Log Analysis
```bash
# Check error patterns
grep -i error logs/main.log | tail -20

# Check success rates
grep -c "success" logs/main_structured.log

# Monitor resource usage
tail -f logs/monitor.log
```

## 📈 Performance Optimization

### Resource Optimization
- Adjust `concurrent_uploads` based on VPS capacity
- Configure `max_video_size_gb` to prevent large file processing
- Set appropriate `rate_limit_delay` to avoid API limits

### Scheduling Optimization
- Stagger different operations to avoid resource conflicts
- Schedule heavy operations during low-traffic hours
- Adjust cleanup frequency based on storage capacity

## 🔄 Maintenance

### Regular Tasks
- Monitor disk space and clean up old files
- Update dependencies regularly
- Review and rotate logs
- Check API rate limits and quotas
- Backup configuration files

### Updates
```bash
# Update the pipeline
git pull origin main

# Restart services
docker-compose restart
# or
sudo systemctl restart youtube-pipeline
```

## 📝 License

This project is for educational purposes. Please ensure compliance with YouTube's Terms of Service and Dailymotion's API Terms of Use.

## ⚠️ Disclaimer

This tool is provided as-is for educational purposes. Users are responsible for:
- Complying with YouTube's Terms of Service
- Respecting copyright laws
- Following Dailymotion's API guidelines
- Ensuring proper licensing for content redistribution

Use at your own risk and responsibility.
