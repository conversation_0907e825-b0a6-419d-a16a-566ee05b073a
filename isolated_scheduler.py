"""
Resource-aware scheduler for multi-project VPS environment
Automatically pauses when other services need resources
"""
import os
import sys
import time
import signal
import threading
import schedule
from datetime import datetime, timedelta
from typing import Dict, Any, Callable
import psutil
import json
from pathlib import Path

from isolated_config import config
from logger import get_logger

logger = get_logger('scheduler')

class IsolatedScheduler:
    """Resource-aware scheduler that respects other VPS services"""
    
    def __init__(self):
        self.running = False
        self.paused = False
        self.jobs = {}
        self.health_stats = {}
        self.last_health_check = None
        self.shutdown_event = threading.Event()
        self.pause_event = threading.Event()
        self.lock = threading.Lock()
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGUSR1, self._pause_handler)   # Custom pause signal
        signal.signal(signal.SIGUSR2, self._resume_handler)  # Custom resume signal
        
        # Initialize monitoring
        self._init_health_monitoring()
        
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.stop()
    
    def _pause_handler(self, signum, frame):
        """Handle pause signal (SIGUSR1)"""
        logger.info("Received pause signal, pausing operations...")
        self.pause()
    
    def _resume_handler(self, signum, frame):
        """Handle resume signal (SIGUSR2)"""
        logger.info("Received resume signal, resuming operations...")
        self.resume()
    
    def _init_health_monitoring(self):
        """Initialize health monitoring"""
        self.health_stats = {
            'start_time': datetime.now(),
            'total_runs': 0,
            'successful_runs': 0,
            'failed_runs': 0,
            'paused_runs': 0,
            'resource_pauses': 0,
            'last_run_time': None,
            'last_success_time': None,
            'last_error_time': None,
            'last_pause_time': None,
            'consecutive_failures': 0,
            'system_stats': {},
            'resource_usage': {},
            'other_processes': {}
        }
    
    def pause(self):
        """Pause all operations"""
        with self.lock:
            if not self.paused:
                self.paused = True
                self.pause_event.set()
                self.health_stats['last_pause_time'] = datetime.now()
                logger.info("Scheduler paused - yielding resources to other services")
    
    def resume(self):
        """Resume operations"""
        with self.lock:
            if self.paused:
                self.paused = False
                self.pause_event.clear()
                logger.info("Scheduler resumed")
    
    def _check_resource_availability(self) -> bool:
        """Check if resources are available for pipeline operations"""
        try:
            # Use isolated config's resource checking
            if config.should_pause_operations():
                return False
            
            # Additional checks for other processes
            high_priority_processes = self._get_high_priority_processes()
            if high_priority_processes:
                logger.info(f"High priority processes detected: {high_priority_processes}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Resource availability check failed: {e}")
            return False
    
    def _get_high_priority_processes(self) -> list:
        """Detect high-priority processes that should take precedence"""
        high_priority = []
        
        try:
            # Processes that should have priority over our pipeline
            priority_process_names = [
                'mysqld', 'postgres', 'redis-server',  # Databases
                'nginx', 'apache2', 'httpd',           # Web servers
                'php-fpm', 'gunicorn', 'uwsgi',        # Application servers
                'docker', 'containerd',                # Container services
                'backup', 'rsync',                     # Backup processes
                'aapanel', 'bt',                       # Panel processes
            ]
            
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    proc_info = proc.info
                    proc_name = proc_info['name'].lower()
                    
                    # Check if it's a high-priority process with high resource usage
                    for priority_name in priority_process_names:
                        if priority_name in proc_name:
                            cpu_percent = proc_info.get('cpu_percent', 0)
                            memory_percent = proc_info.get('memory_percent', 0)
                            
                            # If using significant resources, consider it high priority
                            if cpu_percent > 20 or memory_percent > 10:
                                high_priority.append({
                                    'name': proc_name,
                                    'pid': proc_info['pid'],
                                    'cpu': cpu_percent,
                                    'memory': memory_percent
                                })
                                
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return high_priority
            
        except Exception as e:
            logger.error(f"Failed to check high priority processes: {e}")
            return []
    
    def add_job(self, name: str, func: Callable, schedule_expr: str, **kwargs):
        """Add a scheduled job with resource awareness"""
        try:
            # Parse schedule and add job (same as before)
            if schedule_expr.startswith('*/'):
                interval = int(schedule_expr.split('/')[1].split()[0])
                unit = schedule_expr.split()[1] if len(schedule_expr.split()) > 1 else 'minutes'
                
                if unit.startswith('minute'):
                    job = schedule.every(interval).minutes.do(self._run_job_with_checks, name, func, **kwargs)
                elif unit.startswith('hour'):
                    job = schedule.every(interval).hours.do(self._run_job_with_checks, name, func, **kwargs)
                else:
                    raise ValueError(f"Unsupported unit: {unit}")
            else:
                parts = schedule_expr.split()
                if len(parts) == 5:
                    minute, hour, day, month, weekday = parts
                    
                    if hour != '*' and minute != '*':
                        time_str = f"{hour.zfill(2)}:{minute.zfill(2)}"
                        job = schedule.every().day.at(time_str).do(self._run_job_with_checks, name, func, **kwargs)
                    elif minute != '*' and hour == '*':
                        job = schedule.every().hour.at(f":{minute.zfill(2)}").do(self._run_job_with_checks, name, func, **kwargs)
                    else:
                        raise ValueError(f"Unsupported cron expression: {schedule_expr}")
                else:
                    raise ValueError(f"Invalid cron expression: {schedule_expr}")
            
            self.jobs[name] = {
                'job': job,
                'function': func,
                'schedule': schedule_expr,
                'kwargs': kwargs,
                'last_run': None,
                'last_success': None,
                'last_error': None,
                'run_count': 0,
                'success_count': 0,
                'error_count': 0,
                'pause_count': 0
            }
            
            logger.info(f"Added resource-aware job '{name}' with schedule '{schedule_expr}'")
            
        except Exception as e:
            logger.error(f"Failed to add job '{name}': {e}")
            raise
    
    def _run_job_with_checks(self, name: str, func: Callable, **kwargs):
        """Execute job with resource checks and pausing capability"""
        if self.shutdown_event.is_set():
            return
        
        start_time = datetime.now()
        
        # Check if we should pause due to resource constraints
        if not self._check_resource_availability():
            with self.lock:
                self.jobs[name]['pause_count'] += 1
                self.health_stats['paused_runs'] += 1
                self.health_stats['resource_pauses'] += 1
            
            logger.info(f"Job '{name}' paused due to resource constraints")
            return
        
        # Check if manually paused
        if self.paused:
            logger.info(f"Job '{name}' skipped - scheduler is paused")
            return
        
        # Update job stats
        with self.lock:
            self.jobs[name]['last_run'] = start_time
            self.jobs[name]['run_count'] += 1
            self.health_stats['total_runs'] += 1
        
        logger.info(f"Starting resource-aware job '{name}'")
        
        try:
            # Set lower process priority during execution
            original_nice = os.nice(0)
            os.nice(5)  # Lower priority
            
            # Monitor resources during execution
            resource_monitor = threading.Thread(
                target=self._monitor_job_resources,
                args=(name, start_time),
                daemon=True
            )
            resource_monitor.start()
            
            # Run the actual job
            result = func(**kwargs)
            
            # Restore original priority
            os.nice(original_nice - os.nice(0))
            
            # Record success
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            with self.lock:
                self.jobs[name]['last_success'] = end_time
                self.jobs[name]['success_count'] += 1
                self.health_stats['successful_runs'] += 1
                self.health_stats['last_success_time'] = end_time
                self.health_stats['consecutive_failures'] = 0
            
            logger.info(f"Resource-aware job '{name}' completed successfully in {duration:.2f} seconds")
            
            # Log with resource usage
            logger.log_operation(
                operation=f"isolated_job_{name}",
                status="success",
                duration=duration,
                result=result,
                resource_friendly=True
            )
            
        except Exception as e:
            # Restore priority on error
            try:
                os.nice(original_nice - os.nice(0))
            except:
                pass
            
            # Record failure
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            with self.lock:
                self.jobs[name]['last_error'] = end_time
                self.jobs[name]['error_count'] += 1
                self.health_stats['failed_runs'] += 1
                self.health_stats['last_error_time'] = end_time
                self.health_stats['consecutive_failures'] += 1
            
            logger.error(f"Resource-aware job '{name}' failed after {duration:.2f} seconds: {e}")
            
            # Log failure
            logger.log_operation(
                operation=f"isolated_job_{name}",
                status="failed",
                duration=duration,
                error=str(e),
                resource_friendly=True
            )
    
    def _monitor_job_resources(self, job_name: str, start_time: datetime):
        """Monitor resource usage during job execution"""
        try:
            while True:
                # Check if job is still running (simple heuristic)
                if (datetime.now() - start_time).total_seconds() > 3600:  # 1 hour max
                    break
                
                # Check if resources are still available
                if not self._check_resource_availability():
                    logger.warning(f"Resource constraints detected during job '{job_name}' execution")
                    # Could implement job interruption here if needed
                
                time.sleep(30)  # Check every 30 seconds
                
        except Exception as e:
            logger.error(f"Resource monitoring failed for job '{job_name}': {e}")
    
    def _update_system_stats(self):
        """Update system statistics with isolation awareness"""
        try:
            # Get system stats
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # Get our process stats
            try:
                process = psutil.Process()
                our_stats = {
                    'pid': process.pid,
                    'memory_mb': process.memory_info().rss / 1024 / 1024,
                    'cpu_percent': process.cpu_percent(),
                    'num_threads': process.num_threads(),
                    'nice': process.nice(),
                    'status': process.status()
                }
            except:
                our_stats = {'error': 'Process info unavailable'}
            
            # Get other processes stats
            other_processes = self._get_high_priority_processes()
            
            # Disk usage for our directory only
            base_dir = config.get('base_dir')
            disk_usage = config.get_disk_usage(base_dir)
            
            self.health_stats['system_stats'] = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_gb': memory.available / 1024**3,
                'our_process': our_stats,
                'other_high_priority_processes': len(other_processes),
                'uptime_hours': (datetime.now() - self.health_stats['start_time']).total_seconds() / 3600
            }
            
            self.health_stats['resource_usage'] = disk_usage
            self.health_stats['other_processes'] = other_processes[:5]  # Top 5 only
            
            # Check if we should auto-pause
            if config.get('auto_pause_on_high_load', True):
                if not self._check_resource_availability() and not self.paused:
                    self.pause()
                elif self._check_resource_availability() and self.paused:
                    # Auto-resume after a delay
                    time.sleep(60)  # Wait 1 minute before resuming
                    if self._check_resource_availability():
                        self.resume()
            
        except Exception as e:
            logger.error(f"Failed to update system stats: {e}")
    
    def start(self):
        """Start the resource-aware scheduler"""
        if self.running:
            logger.warning("Scheduler is already running")
            return
        
        self.running = True
        logger.info("Starting isolated pipeline scheduler...")
        logger.info("Scheduler will automatically pause when other services need resources")
        
        # Start health monitoring thread
        health_thread = threading.Thread(target=self._health_check_worker, daemon=True)
        health_thread.start()
        
        # Main scheduler loop
        try:
            while self.running and not self.shutdown_event.is_set():
                if not self.paused:
                    schedule.run_pending()
                else:
                    logger.debug("Scheduler paused, skipping job execution")
                
                time.sleep(10)  # Check every 10 seconds
                
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        except Exception as e:
            logger.error(f"Scheduler error: {e}")
            logger.exception("Scheduler exception details")
        finally:
            self.stop()
    
    def _health_check_worker(self):
        """Background worker for health monitoring"""
        while not self.shutdown_event.is_set():
            try:
                self._update_system_stats()
                self.last_health_check = datetime.now()
                
                # Save health stats
                self._save_health_stats()
                
                # Wait for next check
                self.shutdown_event.wait(config.get('health_check_interval', 600))
                
            except Exception as e:
                logger.error(f"Health check worker error: {e}")
                self.shutdown_event.wait(60)
    
    def _save_health_stats(self):
        """Save health statistics to file"""
        try:
            stats_file = Path(config.get('base_dir')) / 'health_stats.json'
            
            # Convert datetime objects for JSON
            stats_copy = self.health_stats.copy()
            for key, value in stats_copy.items():
                if isinstance(value, datetime):
                    stats_copy[key] = value.isoformat()
            
            # Add job statistics
            stats_copy['jobs'] = {}
            for name, job_info in self.jobs.items():
                stats_copy['jobs'][name] = {
                    'schedule': job_info['schedule'],
                    'run_count': job_info['run_count'],
                    'success_count': job_info['success_count'],
                    'error_count': job_info['error_count'],
                    'pause_count': job_info['pause_count'],
                    'last_run': job_info['last_run'].isoformat() if job_info['last_run'] else None,
                    'last_success': job_info['last_success'].isoformat() if job_info['last_success'] else None,
                    'last_error': job_info['last_error'].isoformat() if job_info['last_error'] else None
                }
            
            with open(stats_file, 'w') as f:
                json.dump(stats_copy, f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to save health stats: {e}")
    
    def stop(self):
        """Stop the scheduler gracefully"""
        if not self.running:
            return
        
        logger.info("Stopping isolated pipeline scheduler...")
        self.running = False
        self.shutdown_event.set()
        
        # Save final stats
        self._save_health_stats()
        
        logger.info("Isolated pipeline scheduler stopped")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current scheduler status"""
        return {
            'running': self.running,
            'paused': self.paused,
            'jobs_count': len(self.jobs),
            'health_stats': self.health_stats,
            'resource_friendly': True,
            'isolation_active': True,
            'last_health_check': self.last_health_check.isoformat() if self.last_health_check else None
        }

# Global isolated scheduler instance
scheduler = IsolatedScheduler()
