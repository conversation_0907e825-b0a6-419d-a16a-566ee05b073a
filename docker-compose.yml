version: '3.8'

services:
  youtube-pipeline:
    build: .
    container_name: youtube-pipeline
    restart: unless-stopped
    
    # Environment variables
    environment:
      - CONFIG_FILE=/app/config/config.json
      - LOG_LEVEL=INFO
      - ENABLE_MONITORING=true
    
    # Volumes for persistent data
    volumes:
      - ./config:/app/config
      - ./downloads:/app/downloads
      - ./modified_videos:/app/modified_videos
      - ./logs:/app/logs
      - ./assets:/app/assets
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    
    # Health check
    healthcheck:
      test: ["CMD", "python", "daemon.py", "status"]
      interval: 5m
      timeout: 30s
      retries: 3
      start_period: 1m
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
    
    # Network mode
    network_mode: "bridge"
    
    # Security options
    security_opt:
      - no-new-privileges:true
    
    # User mapping
    user: "1000:1000"

  # Optional: Monitoring with Prometheus and Grafana
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    profiles:
      - monitoring

  # Optional: Log aggregation with ELK stack
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    profiles:
      - logging

  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: logstash
    restart: unless-stopped
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline
    ports:
      - "5044:5044"
    depends_on:
      - elasticsearch
    profiles:
      - logging

  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    profiles:
      - logging

volumes:
  prometheus_data:
  grafana_data:
  elasticsearch_data:

networks:
  default:
    driver: bridge
