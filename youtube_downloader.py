import yt_dlp
import sys
import os
import json
import time
from datetime import datetime

# Configuration - update these values for your VPS
VPS_COOKIES_PATH = "/home/<USER>/youtube_cookies.txt"
OUTPUT_PATH = "/home/<USER>/downloads"
CHANNELS_FILE_PATH = "/home/<USER>/channels.txt"  # Path to file containing channel URLs
HISTORY_FILE_PATH = "/home/<USER>/download_history.json"  # Tracks downloaded videos
KEYWORDS_FILE_PATH = "/home/<USER>/keywords.txt"  # Path to file containing keywords to filter videos

def read_channels_from_file(file_path):
    """Read channel URLs from a text file, one URL per line."""
    try:
        with open(file_path, 'r') as file:
            channels = [line.strip() for line in file if line.strip()]
        return channels
    except Exception as e:
        print(f"Error reading channels file: {e}")
        return []

def read_keywords_from_file(file_path):
    """Read keywords from a text file, one keyword per line."""
    try:
        with open(file_path, 'r') as file:
            keywords = [line.strip().lower() for line in file if line.strip()]
        return keywords
    except Exception as e:
        print(f"Error reading keywords file: {e}")
        return []

def load_download_history():
    """Load the history of downloaded videos."""
    if os.path.exists(HISTORY_FILE_PATH):
        try:
            with open(HISTORY_FILE_PATH, 'r') as file:
                return json.load(file)
        except Exception as e:
            print(f"Error loading download history: {e}")
    return {}

def save_download_history(history):
    """Save the updated download history."""
    try:
        with open(HISTORY_FILE_PATH, 'w') as file:
            json.dump(history, file, indent=2)
    except Exception as e:
        print(f"Error saving download history: {e}")

def get_channel_videos(channel_url):
    """Get list of videos from a channel."""
    ydl_opts = {
        'quiet': True,
        'extract_flat': True,
        'force_generic_extractor': False,
        'cookiefile': VPS_COOKIES_PATH,
    }
    
    videos = []
    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            result = ydl.extract_info(channel_url, download=False)
            if 'entries' in result:
                videos = [{'id': entry.get('id'), 
                           'url': f"https://www.youtube.com/watch?v={entry.get('id')}", 
                           'title': entry.get('title')} 
                          for entry in result['entries']]
    except Exception as e:
        print(f"Error fetching videos from {channel_url}: {e}")
    
    return videos

def video_contains_keywords(video_title, keywords):
    """Check if video title contains any of the keywords."""
    if not keywords:  # If no keywords provided, accept all videos
        return True
        
    video_title = video_title.lower()
    for keyword in keywords:
        if keyword in video_title:
            return True
    return False

def download_new_videos(channel_url, download_history, keywords, cookies_path=VPS_COOKIES_PATH, output_path=OUTPUT_PATH):
    """Download only new videos from a channel that contain keywords and haven't been downloaded before."""
    channel_videos = get_channel_videos(channel_url)
    
    if not channel_videos:
        print(f"No videos found for channel: {channel_url}")
        return 0
    
    # Get channel ID from the first video
    channel_id = None
    if channel_videos:
        channel_id = channel_url.split('/')[-1]
        if 'channel/' not in channel_url and '@' not in channel_url:
            # Try to get channel ID from the result
            with yt_dlp.YoutubeDL({'quiet': True}) as ydl:
                info = ydl.extract_info(channel_url, download=False)
                if info and 'channel_id' in info:
                    channel_id = info['channel_id']
    
    # Initialize channel in history if not present
    if channel_id not in download_history:
        download_history[channel_id] = []
    
    # Find new videos that match keywords
    new_videos = []
    for video in channel_videos:
        if video['id'] not in download_history[channel_id] and video_contains_keywords(video['title'], keywords):
            new_videos.append(video)
    
    if not new_videos:
        print(f"No new videos matching keywords found for channel: {channel_url}")
        return 0
    
    print(f"Found {len(new_videos)} new videos matching keywords for channel: {channel_url}")
    
    # Download new videos
    options = {
        'format': 'best',
        'cookiefile': cookies_path,
        'outtmpl': f'{output_path}/%(uploader)s/%(title)s.%(ext)s',
        'verbose': True,
        'ignoreerrors': True,
    }
    
    downloaded_count = 0
    with yt_dlp.YoutubeDL(options) as ydl:
        for video in new_videos:
            print(f"\nDownloading: {video['title']} ({video['url']})")
            try:
                ydl.download([video['url']])
                # Add to download history
                download_history[channel_id].append(video['id'])
                downloaded_count += 1
                print(f"Successfully downloaded: {video['title']}")
            except Exception as e:
                print(f"Error downloading {video['url']}: {e}")
    
    return downloaded_count

def check_channels_for_new_videos():
    """Check all channels for new videos matching keywords and download them."""
    channels = read_channels_from_file(CHANNELS_FILE_PATH)
    if not channels:
        print(f"No channels found. Please add channel URLs to {CHANNELS_FILE_PATH}.")
        return
    
    keywords = read_keywords_from_file(KEYWORDS_FILE_PATH)
    if keywords:
        print(f"Filtering videos by {len(keywords)} keywords: {', '.join(keywords)}")
    else:
        print("No keywords found. Will download all new videos.")
    
    download_history = load_download_history()
    total_new_videos = 0
    
    print(f"Checking {len(channels)} channels for new videos...")
    for channel_url in channels:
        print(f"\nChecking channel: {channel_url}")
        new_videos_count = download_new_videos(channel_url, download_history, keywords)
        total_new_videos += new_videos_count
    
    # Save updated download history
    save_download_history(download_history)
    
    if total_new_videos > 0:
        print(f"\nDownloaded {total_new_videos} new videos matching keywords across all channels.")
    else:
        print("\nNo new videos matching keywords found across all channels.")

if __name__ == "__main__":
    # Create output directory if it doesn't exist
    os.makedirs(OUTPUT_PATH, exist_ok=True)
    
    # Create parent directory for history file if it doesn't exist
    os.makedirs(os.path.dirname(HISTORY_FILE_PATH), exist_ok=True)
    
    print(f"YouTube Channel Monitor - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    check_channels_for_new_videos()


