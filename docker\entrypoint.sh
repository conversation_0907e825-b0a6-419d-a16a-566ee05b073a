#!/bin/bash

# Entrypoint script for Docker container
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}YouTube to Dailymotion Pipeline Container${NC}"
echo "=========================================="

# Function to log messages
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

# Create necessary directories
log "Creating directories..."
mkdir -p /app/{downloads,modified_videos,logs,assets,config}

# Set default configuration if not exists
if [ ! -f "/app/config/config.json" ]; then
    log "Creating default configuration..."
    cat > /app/config/config.json << 'EOF'
{
  "base_dir": "/app",
  "download_dir": "/app/downloads",
  "modified_dir": "/app/modified_videos",
  "log_dir": "/app/logs",
  "logo_path": "/app/assets/logo.png",
  "cookies_path": "/app/config/cookies.txt",
  "channels_file": "/app/config/channels.txt",
  "keywords_file": "/app/config/keywords.txt",
  "history_file": "/app/config/download_history.json",
  "uploaded_log": "/app/config/uploaded_videos.log",
  "token_file": "/app/config/token.json",
  
  "channel_name": "Fast in Sport",
  "dailymotion_channel": "sport",
  "dailymotion_tags": ["sports", "highlights", "fastinsport"],
  
  "speed_factor": 1.03,
  "brightness_factor": 1.08,
  "contrast_factor": 1.05,
  "logo_position": "bottom-right",
  "logo_size": 0.12,
  
  "upload_limit": 5,
  "download_limit": 10,
  "concurrent_uploads": 1,
  "max_video_size_gb": 2.0,
  "min_free_space_gb": 5.0,
  
  "run_interval_hours": 6,
  "download_schedule": "0 */6 * * *",
  "upload_schedule": "30 */3 * * *",
  "cleanup_schedule": "0 2 * * *",
  
  "max_retries": 3,
  "retry_delay": 300,
  "rate_limit_delay": 60,
  "health_check_interval": 300,
  
  "enable_monitoring": true,
  "log_level": "INFO",
  "max_log_files": 30,
  "max_log_size_mb": 100
}
EOF
fi

# Create sample files if they don't exist
if [ ! -f "/app/config/channels.txt" ]; then
    log "Creating sample channels file..."
    cat > /app/config/channels.txt << 'EOF'
# Add YouTube channel URLs here, one per line
# Examples:
# https://www.youtube.com/@espn
# https://www.youtube.com/@skysports
# https://www.youtube.com/c/NBCSports
EOF
fi

if [ ! -f "/app/config/keywords.txt" ]; then
    log "Creating sample keywords file..."
    cat > /app/config/keywords.txt << 'EOF'
# Add keywords to filter videos, one per line
highlights
goals
best moments
match
game
sport
football
soccer
basketball
tennis
EOF
fi

# Create empty files if they don't exist
touch /app/config/cookies.txt
if [ ! -f "/app/config/download_history.json" ]; then
    echo '{}' > /app/config/download_history.json
fi
touch /app/config/uploaded_videos.log

# Set permissions
chmod 600 /app/config/cookies.txt /app/config/token.json 2>/dev/null || true

# Validate configuration
log "Validating configuration..."
if ! python daemon.py test; then
    error "Configuration validation failed"
    echo ""
    echo "Please check the following:"
    echo "1. Add YouTube cookies to /app/config/cookies.txt"
    echo "2. Add channel URLs to /app/config/channels.txt"
    echo "3. Configure Dailymotion API credentials in /app/config/config.json"
    echo "4. Add logo image to /app/assets/logo.png"
    echo ""
    warning "Starting in configuration mode. Fix the issues above and restart."
    
    # Keep container running for configuration
    exec tail -f /dev/null
fi

# Handle different commands
case "${1:-start}" in
    start)
        log "Starting YouTube to Dailymotion Pipeline..."
        exec python daemon.py start
        ;;
    
    stop)
        log "Stopping pipeline..."
        python daemon.py stop
        ;;
    
    status)
        python daemon.py status
        ;;
    
    test)
        log "Running pipeline test..."
        python daemon.py test
        ;;
    
    shell)
        log "Starting interactive shell..."
        exec /bin/bash
        ;;
    
    *)
        log "Running custom command: $*"
        exec "$@"
        ;;
esac
